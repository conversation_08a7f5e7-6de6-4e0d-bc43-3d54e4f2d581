/*
 * Copyright (c) 2022-2023 Alibaba Group Holding Ltd.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not use this file except in compliance with
 * the License. You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software distributed under the License is distributed on
 * an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the License for the
 * specific language governing permissions and limitations under the License.
 */
package com.alibaba.higress.sdk.model.wasmplugin;

public final class PluginCategory {

    private PluginCategory() {}

    public static final String AUTH = "auth";
    public static final String SECURITY = "security";
    public static final String PROTOCOL = "protocol";
    public static final String FLOW_CONTROL = "flow-control";
    public static final String FLOW_MONITOR = "flow-monitor";
    public static final String CUSTOM = "custom";
}
