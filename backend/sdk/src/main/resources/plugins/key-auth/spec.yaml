apiVersion: 1.0.0
info:
  gatewayMinVersion: ""
  type: enterprise
  category: auth
  name: key-auth
  image: platform_wasm/key-auth
  title: Key Auth
  x-title-i18n:
    zh-CN: Key 认证
  description: Authentication based on API Key.
  x-description-i18n:
    zh-CN: 基于 API Key 实现身份认证和鉴权。
  iconUrl: https://img.alicdn.com/imgextra/i4/O1CN01BPFGlT1pGZ2VDLgaH_!!6000000005333-2-tps-42-42.png
  version: 1.0.0
  contact:
    name: Higress Team
    url: http://higress.io/
    email: <EMAIL>
spec:
  phase: AUTHN
  priority: 310
  routeConfigSchema:
    openAPIV3Schema:
      type: object
      properties:
        allow:
          type: array
          title: 授权访问的调用方列表
          x-title-i18n:
            en-US: Allowed Consumers
          description: 对于匹配上述条件的请求，允许访问的调用方列表
          x-description-i18n:
            en-US: Consumers to be allowed for matched requests
          items:
            type: string
            example:
              - consumer1
        consumers:
          type: array
          x-scope: GLOBAL
          title: 调用方列表
          x-title-i18n:
            en-US: Consumer List
          description: 服务调用方列表，用于对请求进行认证
          x-description-i18n:
            en-US: List of service consumers which will be used in request authentication
          items:
            type: object
            properties:
              name:
                type: string
                title: 名称
                x-title-i18n:
                  en-US: Name
                description: 该调用方的名称
                x-description-i18n:
                  en-US: The name of the consumer
                example:
                  - consumer1
              credential:
                type: string
                title: 访问凭证
                x-title-i18n:
                  en-US: Credential
                description: 该调用方的访问凭证
                x-description-i18n:
                  en-US: The credential of the consumer
                example:
                  - 2bda943c-ba2b-11ec-ba07-00163e1250b5
            required:
              - name
              - credential
        keys:
          type: array
          x-scope: GLOBAL
          title: API Key 来源字段名称
          x-title-i18n:
            en-US: API Key Names
          description: API Key 的来源字段名称，可以是 URL 参数或者 HTTP 请求头名称
          x-description-i18n:
            en-US: The name of the API Key, which can be a URL parameter or an HTTP request header name.
          items:
            type: string
            example:
              - apikey
        in_query:
          type: boolean
          x-scope: INSTANCE
          title: 判断key是否在URL参数中
          x-title-i18n:
            en-US: Judge Key in Query
          description: 配置 true 时，网关会尝试从 URL 参数中解析 API Key。注意 in_query 和 in_header 至少有一个为 true。
          x-description-i18n:
          en-US: When configured true, the gateway will try to parse the API Key from the URL parameters.Note that at least one of in_query and in_header is true. 
          default: true
        in_header:
          type: boolean
          x-scope: INSTANCE
          title: 判断key是否在HTTP请求头中
          x-title-i18n:
            en-US: Judge Key in Header
          description: 配置 true 时，网关会尝试从 URL 参数中解析 API Key。注意 in_query 和 in_header 至少有一个为 true。
          x-description-i18n:
          en-US: When configured true, the gateway will try to parse the API Key from the HTTP request header.Note that at least one of in_query and in_header is true. 
          default: true
      example:
        allows:
        - consumer1
        - consumer2        
  configSchema:
    openAPIV3Schema:
      type: object
      properties:
        global_auth:
          type: boolean
          x-scope: GLOBAL
          title: 是否开启全局认证
          x-title-i18n:
            en-US: Enable Global Auth
          description: 若不开启全局认证，则全局配置只提供凭证信息。只有在域名或路由上进行了配置才会启用认证。
          x-description-i18n:
            en-US: If set to false, only consumer info will be accepted from the global config. Auth feature shall only be enabled if the corresponding domain or route is configured.
          example: false
        consumers:
          type: array
          x-scope: GLOBAL
          title: 调用方列表
          x-title-i18n:
            en-US: Consumer List
          description: 服务调用方列表，用于对请求进行认证
          x-description-i18n:
            en-US: List of service consumers which will be used in request authentication
          items:
            type: object
            properties:
              name:
                type: string
                title: 名称
                x-title-i18n:
                  en-US: Name
                description: 该调用方的名称
                x-description-i18n:
                  en-US: The name of the consumer
                example:
                  - consumer1
              credential:
                type: string
                title: 访问凭证
                x-title-i18n:
                  en-US: Credential
                description: 该调用方的访问凭证
                x-description-i18n:
                  en-US: The credential of the consumer
                example:
                  - 2bda943c-ba2b-11ec-ba07-00163e1250b5
            required:
              - name
              - credential
        keys:
          type: array
          x-scope: GLOBAL
          title: API Key 来源字段名称
          x-title-i18n:
            en-US: API Key Names
          description: API Key 的来源字段名称，可以是 URL 参数或者 HTTP 请求头名称
          x-description-i18n:
            en-US: The name of the API Key, which can be a URL parameter or an HTTP request header name.
          items:
            type: string
            example:
              - apikey
        in_query:
          type: boolean
          x-scope: INSTANCE
          title: 判断key是否在URL参数中
          x-title-i18n:
            en-US: Judge Key in Query
          description: 配置 true 时，网关会尝试从 URL 参数中解析 API Key。注意 in_query 和 in_header 至少有一个为 true。
          x-description-i18n:
          en-US: When configured true, the gateway will try to parse the API Key from the URL parameters.Note that at least one of in_query and in_header is true. 
          default: true
        in_header:
          type: boolean
          x-scope: INSTANCE
          title: 判断key是否在HTTP请求头中
          x-title-i18n:
            en-US: Judge Key in Header
          description: 配置 true 时，网关会尝试从 URL 参数中解析 API Key。注意 in_query 和 in_header 至少有一个为 true。
          x-description-i18n:
          en-US: When configured true, the gateway will try to parse the API Key from the HTTP request header.Note that at least one of in_query and in_header is true. 
          default: true
      required:
        - consumers
      example:
        global_auth: false
        consumers:
          - credential: key-example1
            name: consumer1
          - credential: key-example2
            name: consumer2
        keys:
          - apikey
        in_query: true
