apiVersion: 1.0.0
info:
  gatewayMinVersion: ""
  type: enterprise
  category: ai
  name: ai-statistics
  image: platform_wasm/ai-statistics
  title: AI Statistics
  x-title-i18n:
    zh-CN: AI 统计
  description: Provides statistics of token usage, including logs, monitoring, and alerts.
  x-description-i18n:
    zh-CN: 提供了对 token 用量的统计信息，包括日志、监控以及告警。
  iconUrl: https://img.alicdn.com/imgextra/i1/O1CN018iKKih1iVx287RltL_!!6000000004419-2-tps-42-42.png
  version: 1.0.0
  contact:
    name: rinfx
spec:
  phase: default
  priority: 900
  configSchema:
    openAPIV3Schema:
      type: object
      example:
  routeConfigSchema:
    openAPIV3Schema:
      type: object
      example:
        # attributes:
        # - key: consumer # 配合认证鉴权记录consumer
        #   value_source: request_header
        #   value: x-mse-consumer
        #   apply_to_log: true   # 是否将信息添加到日志中
        #   apply_to_span: false # 是否将信息添加到链路追踪span attribute中
        # - key: question # 记录问题
        #   value_source: request_body
        #   value: <EMAIL>
        #   apply_to_log: true
        #   apply_to_span: false
        # - key: answer   # 在流式响应中提取大模型的回答
        #   value_source: response_streaming_body
        #   value: choices.0.delta.content
        #   rule: append
        #   apply_to_log: true
        #   apply_to_span: false
        # - key: answer   # 在非流式响应中提取大模型的回答
        #   value_source: response_body
        #   value: choices.0.message.content
        #   apply_to_log: true
        #   apply_to_span: false
