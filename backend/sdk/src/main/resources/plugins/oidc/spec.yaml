apiVersion: 1.0.0
info:
  gatewayMinVersion: ""
  type: enterprise
  category: auth
  name: oidc
  image: platform_wasm/oidc
  title: OIDC Auth
  x-title-i18n:
    zh-CN: OIDC 认证
  description: Implement user authentication based on the OpenID Connect standard.
  x-description-i18n:
    zh-CN: 实现基于 OpenID Connect 标准的用户身份验证。
  iconUrl: https://img.alicdn.com/imgextra/i4/O1CN01BPFGlT1pGZ2VDLgaH_!!6000000005333-2-tps-42-42.png
  version: 1.0.0
  contact:
    name: Jing-ze
spec:
  phase: AUTHN
  priority: 350
  configSchema:
    openAPIV3Schema:
      type: object
      example:
        redirect_url: 'http://foo.bar.com/oauth2/callback'
        oidc_issuer_url: 'https://dev-o43xb1mz7ya7ach4.us.auth0.com/'
        client_id: 'XXXXXXXXXXXXXXXX'
        client_secret: 'XXXXXXXXXXXXXXXX'
        scope: 'openid email offline_access'
        cookie_secret: 'nqavJrGvRmQxWwGNptLdyUVKcBNZ2b18Guc1n_8DCfY='
        service_name: 'auth.dns'
        service_port: 443
        match_type: 'whitelist'
        match_list:
          - match_rule_domain: '*.bar.com'
            match_rule_path: '/foo'
            match_rule_type: 'prefix'
  routeConfigSchema:
    openAPIV3Schema:
      type: object
      example:
        redirect_url: 'http://foo.bar.com/oauth2/callback'
        oidc_issuer_url: 'https://dev-o43xb1mz7ya7ach4.us.auth0.com/'
        client_id: 'XXXXXXXXXXXXXXXX'
        client_secret: 'XXXXXXXXXXXXXXXX'
        scope: 'openid email offline_access'
        cookie_secret: 'nqavJrGvRmQxWwGNptLdyUVKcBNZ2b18Guc1n_8DCfY='
        service_name: 'auth.dns'
        service_port: 443
        match_type: 'whitelist'
        match_list:
          - match_rule_domain: '*.bar.com'
            match_rule_path: '/foo'
            match_rule_type: 'prefix'
