apiVersion: 1.0.0
info:
  gatewayMinVersion: ""
  type: enterprise
  category: auth
  name: jwt-auth
  image: platform_wasm/jwt-auth
  title: JWT Auth
  x-title-i18n:
    zh-CN: JWT 认证
  description: Implement an authentication and authorization feature based on JSON Web Tokens, which supports extracting JWT from URL parameters, request headers and cookies, and checking whether the given token is allowed to access the corresponding resource.
  x-description-i18n:
    zh-CN: 实现了基于 JSON Web Token 进行认证鉴权的功能，支持从 HTTP 请求的 URL 参数、请求头、Cookie 字段解析 JWT，同时验证该 Token 是否有权限访问。
  iconUrl: https://img.alicdn.com/imgextra/i4/O1CN01BPFGlT1pGZ2VDLgaH_!!6000000005333-2-tps-42-42.png
  version: 1.0.0
  contact:
    name: Higress Team
    url: http://higress.io/
    email: <EMAIL>
spec:
  phase: AUTHN
  priority: 340
  routeConfigSchema:
    openAPIV3Schema:
      type: object
      example:
        allows:
        - consumer1
  configSchema:
    openAPIV3Schema:
      type: object
      example:
        global_auth: false
        consumers:
          - name: consumer1
            issuer: abcd
            jwks: |
              {
                "keys": [
                  {
                    "kty": "oct",
                    "kid": "123",
                    "k": "hM0k3AbXBPpKOGg__Ql2Obcq7s60myWDpbHXzgKUQdYo7YCRp0gUqkCnbGSvZ2rGEl4YFkKqIqW7mTHdj-bcqXpNr-NOznEyMpVPOIlqG_NWVC3dydBgcsIZIdD-MR2AQceEaxriPA_VmiUCwfwL2Bhs6_i7eolXoY11EapLQtutz0BV6ZxQQ4dYUmct--7PLNb4BWJyQeWu0QfbIthnvhYllyl2dgeLTEJT58wzFz5HeNMNz8ohY5K0XaKAe5cepryqoXLhA-V-O1OjSG8lCNdKS09OY6O0fkyweKEtuDfien5tHHSsHXoAxYEHPFcSRL4bFPLZ0orTt1_4zpyfew",
                    "alg": "HS256"
                  }
                ]
              }
