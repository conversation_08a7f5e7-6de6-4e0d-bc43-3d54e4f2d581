apiVersion: 1.0.0
info:
  gatewayMinVersion: ""
  type: enterprise
  category: ai
  name: ai-token-ratelimit
  image: platform_wasm/ai-token-ratelimit
  title: AI Token Rate Limit
  x-title-i18n:
    zh-CN: AI Token 限流
  description: Implement token rate limiting based on specific keys, where the key source can be URL parameters, HTTP request headers, client IP addresses, etc.
  x-description-i18n:
    zh-CN: 基于特定键值实现 token 限流，键值来源可以是 URL 参数、HTTP 请求头、客户端 IP 地址等。
  iconUrl: https://img.alicdn.com/imgextra/i1/O1CN018iKKih1iVx287RltL_!!6000000004419-2-tps-42-42.png
  version: 1.0.0
  contact:
    name: rinfx
spec:
  phase: default
  priority: 600
  configSchema:
    openAPIV3Schema:
      type: object
      example:
        rule_name: default_rule
        rule_items:
          - limit_by_per_ip: from-remote-addr
            limit_keys:
              - key: 0.0.0.0/0
                token_per_minute: 100
        redis:
          service_name: redis.static
          service_port: 6379
          username: default
          password: '123456'
  routeConfigSchema:
    openAPIV3Schema:
      type: object
      example:
        rule_name: default_rule
        rule_items:
          - limit_by_per_ip: from-remote-addr
            limit_keys:
              - key: 0.0.0.0/0
                token_per_minute: 100
        redis:
          service_name: redis.static
          service_port: 6379
          username: default
          password: '123456'

