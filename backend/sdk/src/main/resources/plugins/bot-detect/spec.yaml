apiVersion: 1.0.0
info:
  gatewayMinVersion: ""
  type: enterprise
  category: security
  name: bot-detect
  image: platform_wasm/bot-detect
  title: Bot Detect
  x-title-i18n:
    zh-CN: 机器人拦截
  description: Identify and block resource crawling from bots on the Internet.
  x-description-i18n:
    zh-CN: 用于识别并阻止互联网爬虫对站点资源的爬取。
  iconUrl: https://img.alicdn.com/imgextra/i1/O1CN01jKT9vC1O059vNaq5u_!!6000000001642-2-tps-42-42.png
  version: 1.0.0
  contact:
    name: Higress Team
    url: http://higress.io/
    email: <EMAIL>
spec:
  phase: AUTHZ
  priority: 310
  configSchema:
    openAPIV3Schema:
      type: object
      example:
        blocked_code: 404
        blocked_message: "resource not found"
  routeConfigSchema:
    openAPIV3Schema:
      type: object
      example:
        blocked_code: 404
        blocked_message: "resource not found"
