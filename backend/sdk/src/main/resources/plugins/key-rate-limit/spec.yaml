apiVersion: 1.0.0
info:
  gatewayMinVersion: ""
  type: enterprise
  category: traffic
  name: key-rate-limit
  image: platform_wasm/key-rate-limit
  title: Key Rate Limit
  x-title-i18n:
    zh-CN: 基于 Key 限流
  description: Perform rate-limiting based on given keys and values, which can be extracted from URL parameters and HTTP request headers.
  x-description-i18n:
    zh-CN: 根据特定键值实现限流，键值来源可以是 URL 参数、HTTP 请求头。
  iconUrl: https://img.alicdn.com/imgextra/i3/O1CN01bAFa9k1t1gdQcVTH0_!!6000000005842-2-tps-42-42.png
  version: 1.0.0
  contact:
    name: Higress Team
    url: http://higress.io/
    email: <EMAIL>
spec:
  phase: default
  priority: 10
  configSchema:
    openAPIV3Schema:
      type: object
      example:
        limit_by_header: x-api-key
        limit_keys:
          - key: example-key-a
            query_per_second: 10
          - key: example-key-b
            query_per_minute: 1000
  routeConfigSchema:
    openAPIV3Schema:
      type: object
      example:
        limit_by_header: x-api-key
        limit_keys:
          - key: example-key-a
            query_per_second: 10
          - key: example-key-b
            query_per_minute: 1000
