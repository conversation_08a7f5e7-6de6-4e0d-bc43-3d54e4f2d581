apiVersion: 1.0.0
info:
  gatewayMinVersion: "2.0.0"
  type: enterprise
  category: ai
  image: platform_wasm/ai-security-guard
  name: ai-security-guard
  title: AI Security Guard
  x-title-i18n:
    zh-CN: AI 内容安全
  description: Securely check the input and output of large models based on Alibaba Cloud Content Moderation Service.
  x-description-i18n:
    zh-CN: 基于阿里云内容安全服务对大模型的输入输出进行安全检测。
  iconUrl: https://img.alicdn.com/imgextra/i1/O1CN018iKKih1iVx287RltL_!!6000000004419-2-tps-42-42.png
  version: 1.0.0
  contact:
    name: rinfx
spec:
  phase: default
  priority: 300
  configSchema:
    openAPIV3Schema:
      type: object
      example:
        serviceName: safecheck.dns # 请先创建好相应服务
        servicePort: 443
        serviceHost: green-cip.cn-hangzhou.aliyuncs.com
        accessKey: "XXXXXXXXX"
        secretKey: "XXXXXXXXXXXXXXX"
        checkRequest: true
        checkResponse: false
  routeConfigSchema:
    openAPIV3Schema:
      type: object
      example:
        serviceName: safecheck.dns # 请先创建好相应服务
        servicePort: 443
        serviceHost: green-cip.cn-hangzhou.aliyuncs.com
        accessKey: "XXXXXXXXX"
        secretKey: "XXXXXXXXXXXXXXX"
        checkRequest: true
        checkResponse: false

