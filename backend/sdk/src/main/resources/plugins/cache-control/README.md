---
title: 浏览器缓存控制
keywords: [higress,浏览器缓存控制]
description: 浏览器缓存控制插件配置参考
---

## 功能说明
`cache-control`插件实现了基于 URL 文件后缀来为请求的响应头部添加 `Expires` 和 `Cache-Control` 头部，从而方便浏览器对特定后缀的文件进行缓存，例如 `jpg`、`png` 等图片文件。

## 运行属性

插件执行阶段：`认证阶段`
插件执行优先级：`420`

## 配置字段

| 名称      | 数据类型   | 填写要求                                                                                                | 默认值 | 描述                       |
|---------|--------|-----------------------------------------------------------------------------------------------------|-|--------------------------|
| suffix  | string | 选填，表示匹配的文件后缀名，例如 `jpg`、`png` 等。<br/>如果需要匹配多种后缀，需要用 `\|` 进行分割，例如 `png\|jpg`。<br/>如果不填写，表示匹配所有后缀      |   -  | 配置用于匹配的请求文件后缀            |
| expires | string | 必填，表示缓存的最长时间。<br/>当填入的字符串为数字时，单位为秒，例如需要缓存1小时，需填写 3600。<br/>另外，还可以填写 epoch 或 max<br/>，与 nginx 中语义相同。 | - | 配置缓存的最大时间                |

## 配置示例
1. 缓存后缀为 `jpg`, `png`, `jpeg` 的文件，缓存时间为一小时
```yaml
suffix: jpg|png|jpeg
expires: 3600
```

根据该配置，下列请求在访问时，将会在响应头中添加 `Expires` 和 `Cache-Control` 字段，且过期时间为 1 小时后。

```bash
curl http://example.com/test.png
curl http://exmaple.com/test.jpg
```
2. 缓存所有文件，且缓存至最大时间 `“Thu, 31 Dec 2037 23:55:55 GMT”`
```yaml
expires: max 
```

