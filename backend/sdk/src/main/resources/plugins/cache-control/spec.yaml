apiVersion: 1.0.0
info:
  gatewayMinVersion: ""
  type: oss
  category: transform
  name: cache-control
  image: platform_wasm/cache-control
  title: Browser Cache Control
  x-title-i18n:
    zh-CN: 浏览器缓存控制
  description: Add Expires and Cache-Control headers to the response headers to facilitate browser caching of specific file types, such as jpg, png, and other image files.
  x-description-i18n:
    zh-CN: 为响应头部添加 Expires 和 Cache-Control 头部，从而方便浏览器对特定后缀的文件进行缓存，例如 jpg、png 等图片文件。
  iconUrl: https://img.alicdn.com/imgextra/i3/O1CN01bAFa9k1t1gdQcVTH0_!!6000000005842-2-tps-42-42.png
  version: 1.0.0
  contact:
    name: Higress Team
    url: http://higress.io/
    email: <EMAIL>
spec:
  phase: AUTHN
  priority: 420
  configSchema:
    openAPIV3Schema:
      type: object
      example:
        suffix: jpg|png|jpeg
        expires: 3600
  routeConfigSchema:
    openAPIV3Schema:
      type: object
      example:
        suffix: jpg|png|jpeg
        expires: 3600
