apiVersion: 1.0.0
info:
  gatewayMinVersion: ""
  type: enterprise
  category: ai
  image: platform_wasm/prompt-template
  name: ai-prompt-template
  title: AI Prompt Template
  x-title-i18n:
    zh-CN: AI 提示词模板
  description: Quickly construct fixed-format prompts based on templates.
  x-description-i18n:
    zh-CN: 基于模板快速构建固定格式的提示词。
  iconUrl: https://img.alicdn.com/imgextra/i1/O1CN018iKKih1iVx287RltL_!!6000000004419-2-tps-42-42.png
  version: 1.0.0
  contact:
    name: rinfx
spec:
  phase: default
  priority: 500
  configSchema:
    openAPIV3Schema:
      type: object
      example:
        templates:
        - name: developer-chat
          template:
            model: gpt-3.5-turbo
            messages:
            - role: system
              content: '你是一个 {{program}} 专家，编程语言为 {{language}}'
            - role: user
              content: '帮我写一个 {{program}} 程序'
  routeConfigSchema:
    openAPIV3Schema:
      type: object
      example:
        templates:
        - name: developer-chat
          template:
            model: gpt-3.5-turbo
            messages:
            - role: system
              content: '你是一个 {{program}} 专家，编程语言为 {{language}}'
            - role: user
              content: '帮我写一个 {{program}} 程序'
