apiVersion: 1.0.0
info:
  type: enterprise
  category: ai
  image: platform_wasm/ai-cache
  name: ai-cache
  title: AI Cache
  x-title-i18n:
    zh-CN: AI 缓存
  description: Cache the response of large language models, significantly reduce the response latency of similar problems and save costs.
  x-description-i18n:
    zh-CN: 缓存大语言模型的响应结果，显著降低相似问题的响应时延并节省成本。
  iconUrl: https://img.alicdn.com/imgextra/i1/O1CN018iKKih1iVx287RltL_!!6000000004419-2-tps-42-42.png
  version: 1.0.0
  contact:
    name: john<PERSON><PERSON>
  gatewayMinVersion: "2.0.0"    
spec:
  phase: default
  priority: 800
  configSchema:
    openAPIV3Schema:
      type: object
      example:
        redis:
          serviceName: my-redis.dns
          timeout: 2000
        cacheKeyFrom: "<EMAIL>"
        cacheValueFrom: "choices.0.message.content"
        cacheStreamValueFrom: "choices.0.delta.content"
        returnResponseTemplate: |
          {"id":"from-cache","choices":[{"index":0,"message":{"role":"assistant","content":"%s"},"finish_reason":"stop"}],"model":"gpt-4o","object":"chat.completion","usage":{"prompt_tokens":0,"completion_tokens":0,"total_tokens":0}}
        returnStreamResponseTemplate: |
          data:{"id":"from-cache","choices":[{"index":0,"delta":{"role":"assistant","content":"%s"},"finish_reason":"stop"}],"model":"gpt-4o","object":"chat.completion","usage":{"prompt_tokens":0,"completion_tokens":0,"total_tokens":0}}

          data:[DONE]
  routeConfigSchema:
    openAPIV3Schema:
      type: object
      example:
        redis:
          serviceName: my-redis.dns
          timeout: 2000
        cacheKeyFrom: "<EMAIL>"
        cacheValueFrom: "choices.0.message.content"
        cacheStreamValueFrom: "choices.0.delta.content"
        returnResponseTemplate: |
          {"id":"from-cache","choices":[{"index":0,"message":{"role":"assistant","content":"%s"},"finish_reason":"stop"}],"model":"gpt-4o","object":"chat.completion","usage":{"prompt_tokens":0,"completion_tokens":0,"total_tokens":0}}
        returnStreamResponseTemplate: |
          data:{"id":"from-cache","choices":[{"index":0,"delta":{"role":"assistant","content":"%s"},"finish_reason":"stop"}],"model":"gpt-4o","object":"chat.completion","usage":{"prompt_tokens":0,"completion_tokens":0,"total_tokens":0}}

          data:[DONE]
