apiVersion: 1.0.0
info:
  gatewayMinVersion: "2.0.0"
  type: enterprise
  category: ai
  name: ai-transformer
  image: platform_wasm/ai-transformer
  title: AI Transformer
  x-title-i18n:
    zh-CN: AI 请求/响应转换
  description: Modify the requests/responses of the gateway in a natural language way without writing code.
  x-description-i18n:
    zh-CN: 无须编写代码，使用自然语言的方式对网关的请求/响应进行修改。
  iconUrl: https://img.alicdn.com/imgextra/i1/O1CN018iKKih1iVx287RltL_!!6000000004419-2-tps-42-42.png
  version: 1.0.0
  contact:
    name: rinfx
spec:
  phase: default
  priority: 550
  configSchema:
    openAPIV3Schema:
      type: object
      example:
        request:
          enable: false
          prompt: "如果请求path是以/httpbin开头的，帮我去掉/httpbin前缀，其他的不要改。"
        response:
          enable: true
          prompt: "帮我修改以下HTTP应答信息，要求：1. content-type修改为application/json；2. body由xml转化为json；3.移除content-length。"
        provider:
          serviceName: qwen
          domain: dashscope.aliyuncs.com
          sk: sk-xxxxxxxxxxxxxxxxxxx
  routeConfigSchema:
    openAPIV3Schema:
      type: object
      example:
        request:
          enable: false
          prompt: "如果请求path是以/httpbin开头的，帮我去掉/httpbin前缀，其他的不要改。"
        response:
          enable: true
          prompt: "帮我修改以下HTTP应答信息，要求：1. content-type修改为application/json；2. body由xml转化为json；3.移除content-length。"
        provider:
          serviceName: qwen
          domain: dashscope.aliyuncs.com
          sk: sk-xxxxxxxxxxxxxxxxxxx
