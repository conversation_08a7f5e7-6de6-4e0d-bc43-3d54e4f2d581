apiVersion: 1.0.0
info:
  gatewayMinVersion: ""
  type: oss
  category: transform
  name: de-graphql
  image: platform_wasm/de-graphql
  title: DeGraphQL
  x-title-i18n:
    zh-CN: DeGraphQL
  description: Convert a RESTful API to a GraphQL request.
  x-description-i18n:
    zh-CN: 将 Restful API 转换为 GraphQL 请求。
  iconUrl: https://img.alicdn.com/imgextra/i3/O1CN01bAFa9k1t1gdQcVTH0_!!6000000005842-2-tps-42-42.png
  version: 1.0.0
  contact:
    name: Higress Team
    url: http://higress.io/
    email: <EMAIL>
spec:
  phase: AUTHN
  priority: 430
  configSchema:
    openAPIV3Schema:
      type: object
      example:
        timeout: 5000
        endpoint: /graphql
        domain: api.github.com
        gql: |
          query ($owner:String! $name:String!){
             repository(owner:$owner, name:$name) {
               name
               forkCount
               description
            }
          }
  routeConfigSchema:
    openAPIV3Schema:
      type: object
      example:
        timeout: 5000
        endpoint: /graphql
        domain: api.github.com
        gql: |
          query ($owner:String! $name:String!){
             repository(owner:$owner, name:$name) {
               name
               forkCount
               description
            }
          }
