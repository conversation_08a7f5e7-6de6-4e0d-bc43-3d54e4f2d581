apiVersion: 1.0.0
info:
  gatewayMinVersion: ""
  type: oss
  category: security
  name: cors
  image: platform_wasm/cors
  title: CORS
  x-title-i18n:
    zh-CN: 跨域资源共享
  description: Enables CORS (Cross-Origin Resource Sharing) HTTP response headers for the server.
  x-description-i18n:
    zh-CN: 为服务端启用 CORS（Cross-Origin Resource Sharing，跨域资源共享）的返回 HTTP 响应头。
  iconUrl: https://img.alicdn.com/imgextra/i1/O1CN01jKT9vC1O059vNaq5u_!!6000000001642-2-tps-42-42.png
  version: 1.0.0
  contact:
    name: Higress Team
    url: http://higress.io/
    email: <EMAIL>
spec:
  phase: AUTHZ
  priority: 340
  configSchema:
    openAPIV3Schema:
      type: object
      example:
        allow_origins:
          - '*'
        allow_methods:
          - '*'
        allow_headers:
          - '*'
        expose_headers:
          - '*'
        allow_credentials: false
        max_age: 7200
  routeConfigSchema:
    openAPIV3Schema:
      type: object
      example:
        allow_origins:
          - '*'
        allow_methods:
          - '*'
        allow_headers:
          - '*'
        expose_headers:
          - '*'
        allow_credentials: false
        max_age: 7200

