apiVersion: 1.0.0
info:
  gatewayMinVersion: "2.0.0"
  type: oss
  category: ai
  image: platform_wasm/ai-history
  name: ai-history
  title: AI History
  x-title-i18n:
    zh-CN: AI 历史对话
  description: Automatically cache the corresponding user's historical dialogue and automatically fill it in the context in subsequent dialogues.
  x-description-i18n:
    zh-CN: 自动缓存对应用户的历史对话，在后续对话中自动填充到上下文。
  iconUrl: https://img.alicdn.com/imgextra/i1/O1CN018iKKih1iVx287RltL_!!6000000004419-2-tps-42-42.png
  version: 1.0.0
  contact:
    name: yuemingming
spec:
  phase: default
  priority: 650
  configSchema:
    openAPIV3Schema:
      type: object
      example:
        redis:
          serviceName: my-redis.dns
          timeout: 2000
        fillHistoryCnt: 3
        cacheTTL: 3600
  routeConfigSchema:
    openAPIV3Schema:
      type: object
      example:
        redis:
          serviceName: my-redis.dns
          timeout: 2000
        fillHistoryCnt: 3
        cacheTTL: 3600
