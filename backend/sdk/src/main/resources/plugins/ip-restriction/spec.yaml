apiVersion: 1.0.0
info:
  gatewayMinVersion: ""
  type: oss
  category: security
  name: ip-restriction
  image: platform_wasm/ip-restriction
  title: IP Restriction
  x-title-i18n:
    zh-CN: IP 限制
  description: Add IP addresses to a whitelist or blacklist to restrict access to services or routes.
  x-description-i18n:
    zh-CN: 通过将 IP 地址列入白名单或黑名单来限制对服务或路由的访问。
  iconUrl: https://img.alicdn.com/imgextra/i3/O1CN01bAFa9k1t1gdQcVTH0_!!6000000005842-2-tps-42-42.png
  version: 1.0.0
  contact:
    name: Higress Team
    url: http://higress.io/
    email: <EMAIL>
spec:
  phase: AUTHN
  priority: 210
  configSchema:
    openAPIV3Schema:
      type: object
      example:
        ip_source_type: origin-source
        allow:
          - ********
          - ***********/16
  routeConfigSchema:
    openAPIV3Schema:
      type: object
      example:
        ip_source_type: origin-source
        allow:
          - ********
          - ***********/16
