apiVersion: 1.0.0
info:
  gatewayMinVersion: ""  
  type: enterprise
  category: security
  name: waf
  image: platform_wasm/waf
  title: WAF
  x-title-i18n:
    zh-CN: WAF 防护
  description: Support configuring WAF rules based on OWASP ModSecurity Core Rule Set (CRS).
  x-description-i18n:
    zh-CN: 支持基于 OWASP ModSecurity Core Rule Set (CRS) 的 WAF 规则配置。
  iconUrl: https://img.alicdn.com/imgextra/i1/O1CN01jKT9vC1O059vNaq5u_!!6000000001642-2-tps-42-42.png
  version: 1.0.0
  contact:
    name: Higress Team
    url: http://higress.io/
    email: <EMAIL>
spec:
  phase: AUTHZ
  priority: 330
  configSchema:
    openAPIV3Schema:
      type: object
      example:
        useCRS: true
  routeConfigSchema:
    openAPIV3Schema:
      type: object
      example:
        useCRS: true
