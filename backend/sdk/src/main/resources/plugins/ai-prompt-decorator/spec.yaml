apiVersion: 1.0.0
info:
  gatewayMinVersion: ""
  type: enterprise
  category: ai
  image: platform_wasm/prompt-decorator
  name: ai-prompt-decorator
  title: AI Prompt Decorator
  x-title-i18n:
    zh-CN: AI 提示词
  description: Add additional prompts before and after the user's input to simplify the interaction between the user and the large language model.
  x-description-i18n:
    zh-CN: 在用户输入的提示词前后添加额外的修饰，简化用户与大语言模型的交互。
  iconUrl: https://img.alicdn.com/imgextra/i1/O1CN018iKKih1iVx287RltL_!!6000000004419-2-tps-42-42.png
  version: 1.0.0
  contact:
    name: rinfx
spec:
  phase: default
  priority: 450
  configSchema:
    openAPIV3Schema:
      type: object
      example:
        prepend:
        - role: system
          content: "请使用英语回答问题"
        append:
        - role: user
          content: "每次回答完问题，尝试进行反问"
  routeConfigSchema:
    openAPIV3Schema:
      type: object
      example:
        prepend:
        - role: system
          content: "请使用英语回答问题"
        append:
        - role: user
          content: "每次回答完问题，尝试进行反问"
