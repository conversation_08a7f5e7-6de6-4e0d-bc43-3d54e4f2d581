apiVersion: 1.0.0
info:
  gatewayMinVersion: ""
  type: enterprise
  category: transform
  name: custom-response
  image: platform_wasm/custom-response
  title: Custom Response
  x-title-i18n:
    zh-CN: 自定义应答
  description: Support configuring custom responses, including HTTP response status code, header and body.
  x-description-i18n:
    zh-CN: 支持配置自定义的响应，包括自定义 HTTP 应答状态码、HTTP 应答头，以及 HTTP 应答 Body。
  iconUrl: https://img.alicdn.com/imgextra/i3/O1CN01bAFa9k1t1gdQcVTH0_!!6000000005842-2-tps-42-42.png
  version: 1.0.0
  contact:
    name: Higress Team
    url: http://higress.io/
    email: <EMAIL>
spec:
  phase: AUTHN
  priority: 910
  configSchema:
    openAPIV3Schema:
      type: object
      example:
        status_code: 200
        headers:
          - Content-Type=application/json
        body: "{\"hello\":\"world\"}"
  routeConfigSchema:
    openAPIV3Schema:
      type: object
      example:
        status_code: 200
        headers:
          - Content-Type=application/json
        body: "{\"hello\":\"world\"}"
