apiVersion: 1.0.0
info:
  gatewayMinVersion: ""
  type: enterprise
  category: traffic
  name: cluster-key-rate-limit
  image: platform_wasm/cluster-key-rate-limit
  title: Key Cluster Rate Limit
  x-title-i18n:
    zh-CN: 基于 Key 集群限流
  description: Implement cluster-level rate limiting based on specific key values, which can be derived from URL parameters, HTTP request headers, client IP addresses, etc.
  x-description-i18n:
    zh-CN: 根据特定键值实现集群层面的限流，键值来源可以是 URL 参数、HTTP 请求头、客户端 IP 地址等。
  iconUrl: https://img.alicdn.com/imgextra/i3/O1CN01bAFa9k1t1gdQcVTH0_!!6000000005842-2-tps-42-42.png
  version: 1.0.0
  contact:
    name: Higress Team
    url: http://higress.io/
    email: <EMAIL>
spec:
  phase: default
  priority: 20
  configSchema:
    openAPIV3Schema:
      type: object
      example:
        rule_name: default_rule
        rule_items:
        - limit_by_param: apikey
          limit_keys:
          - key: 9a342114-ba8a-11ec-b1bf-00163e1250b5
            query_per_minute: 10
          - key: a6a6d7f2-ba8a-11ec-bec2-00163e1250b5
            query_per_hour: 100
        - limit_by_per_param: apikey
          limit_keys:
          - key: "regexp:^a.*"
            query_per_second: 10
          - key: "regexp:^b.*"
            query_per_minute: 100
          - key: "*"
            query_per_hour: 1000
        redis:
          service_name: redis.static
        show_limit_quota_header: true
  routeConfigSchema:
    openAPIV3Schema:
      type: object
      example:
        rule_name: default_rule
        rule_items:
        - limit_by_param: apikey
          limit_keys:
          - key: 9a342114-ba8a-11ec-b1bf-00163e1250b5
            query_per_minute: 10
          - key: a6a6d7f2-ba8a-11ec-bec2-00163e1250b5
            query_per_hour: 100
        - limit_by_per_param: apikey
          limit_keys:
          - key: "regexp:^a.*"
            query_per_second: 10
          - key: "regexp:^b.*"
            query_per_minute: 100
          - key: "*"
            query_per_hour: 1000
        redis:
          service_name: redis.static
        show_limit_quota_header: true
