#
# Copyright (c) 2022-2024 Alibaba Group Holding Ltd.
#
# Licensed under the Apache License, Version 2.0 (the "License"); you may not use this file except in compliance with
# the License. You may obtain a copy of the License at
#
# http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software distributed under the License is distributed on
# an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the License for the
# specific language governing permissions and limitations under the License.
#

apiVersion: networking.istio.io/v1alpha3
kind: EnvoyFilter
metadata:
  name: "${name}"
  namespace: higress-system
spec:
  configPatches:
    - applyTo: HTTP_ROUTE
      match:
        context: GATEWAY
        routeConfiguration:
          vhost:
            route:
              name: "${routeName}"
      patch:
        operation: MERGE
        value:
          typed_per_filter_config:
            envoy.filters.http.custom_response:
              "@type": type.googleapis.com/udpa.type.v1.TypedStruct
              type_url: type.googleapis.com/envoy.extensions.filters.http.custom_response.v3.CustomResponse
              value:
                custom_response_matcher:
                  matcher_list:
                    matchers:
                      - predicate:
                          #if( !$responseCodes || $responseCodes.size() == 0 )
                          #set( $responseCodes = ["4xx", "5xx"] )
                          #end
                          #if( $responseCodes.size() == 1 )
                          single_predicate:
                            input:
                              name: "${responseCodes[0]}_response"
                              # 匹配状态码所属的类别，例如4xx，5xx。只匹配精确的状态码，需要使用 HttpResponseStatusCodeMatchInput
                              typed_config:
                                "@type": type.googleapis.com/envoy.type.matcher.v3.HttpResponseStatusCodeClassMatchInput
                            value_match:
                              exact: "${responseCodes[0]}"
                          #else
                          or_matcher:
                            predicate:
                              #foreach( $responseCode in $responseCodes )
                              - single_predicate:
                                  input:
                                    name: "${responseCode}_response"
                                    typed_config:
                                      "@type": type.googleapis.com/envoy.type.matcher.v3.HttpResponseStatusCodeClassMatchInput
                                  value_match:
                                    exact: "${responseCode}"
                              #end
                          #end
                        on_match:
                          action:
                            name: action
                            typed_config:
                              "@type": type.googleapis.com/udpa.type.v1.TypedStruct
                              type_url: type.googleapis.com/envoy.extensions.http.custom_response.redirect_policy.v3.RedirectPolicy
                              value:
                                # 最大重定向 10 次
                                max_internal_redirects: 10
                                # 使用原始的 uri 进行重试
                                use_original_request_uri: true
                                # 使用新请求的response code
                                keep_original_response_code: false
                                # 使用原始的请求 body 进行重试
                                use_original_request_body: true
                                # 非后端服务返回的状态码，而是网关产生的状态码，也会重试，例如命中限流返回4xx，匹配到上面的规则
                                only_redirect_upstream_code: false
                                # 请求头添加这个 header，用于重试时匹配到对应的 fallback 路由
                                request_headers_to_add:
                                  - header:
                                      key: "${fallbackHeader}"
                                      value: "${routeName}"
                                    append: false
                                # 响应头添加这个 header，用于感知当前请求是否发生了 fallback 重试
                                response_headers_to_add:
                                  - header:
                                      key: "${fallbackHeader}"
                                      value: "${routeName}"
                                    append: false