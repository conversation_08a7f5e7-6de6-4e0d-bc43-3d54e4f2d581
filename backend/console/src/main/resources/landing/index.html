<!DOCTYPE html>
<html>
<head>
  <title>Welcome to Higress!</title>
  <style>
      html {
          background-color: #121316;
          color: rgb(235, 236, 239);
      }

      body {
          width: 35em;
          margin: 0 auto;
          font-family: <PERSON><PERSON><PERSON>, <PERSON>erd<PERSON>, <PERSON><PERSON>, sans-serif;
      }

      a:visited, a:hover, a:active {
          color: #f0f0f0;
      }
  </style>
</head>
<body>
<h1>Thanks for using Higress!</h1>
<p>
  Higress is successfully installed and is functioning properly.
  Higress Console is available for further configuration.
</p>
<p>
  For online documentation, please visit <a href="https://higress.cn/" target="_blank">higress.cn</a>
  or <a href="https://github.com/alibaba/higress" target="_blank">alibaba/higress on GitHub</a>.
</p>
<p><em>Happy Higressing!</em></p>
<p style="text-align: center">
  <a href="https://github.com/alibaba/higress" target="_blank">
    <img alt="Higress" style="width: 100px; margin-top: 20px"
         src="data:image/jpeg;base64,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">
  </a>
</p>
</body>
</html>
