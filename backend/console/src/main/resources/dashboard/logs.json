{"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "grafana", "uid": "-- <PERSON><PERSON> --"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "target": {"limit": 100, "matchAny": false, "tags": [], "type": "dashboard"}, "type": "dashboard"}]}, "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 0, "links": [], "liveNow": false, "panels": [{"datasource": {"type": "loki", "uid": "${datasource.id}"}, "gridPos": {"h": 9, "w": 24, "x": 0, "y": 0}, "id": 2, "options": {"dedupStrategy": "none", "enableLogDetails": true, "prettifyLogMessage": false, "showCommonLabels": false, "showLabels": false, "showTime": true, "sortOrder": "Descending", "wrapLogMessage": false}, "targets": [{"datasource": {"type": "loki", "uid": "${datasource.id}"}, "editorMode": "builder", "expr": "{authority=~\".+\"} |= ``", "queryType": "range", "refId": "A"}], "title": "Access Logs", "type": "logs"}], "schemaVersion": 37, "style": "dark", "tags": [], "templating": {"list": []}, "time": {"from": "now-6h", "to": "now"}, "timepicker": {}, "timezone": "", "title": "Higress Access Logs", "weekStart": ""}