{"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "grafana", "uid": "-- <PERSON><PERSON> --"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "target": {"limit": 100, "matchAny": false, "tags": [], "type": "dashboard"}, "type": "dashboard"}]}, "description": "Higress Ingress Controller Dashboard", "editable": true, "fiscalYearStartMonth": 0, "gnetId": 17998, "graphTooltip": 0, "links": [], "liveNow": false, "panels": [{"collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 0}, "id": 2, "panels": [], "title": "General", "type": "row"}, {"datasource": {"type": "prometheus", "uid": "${datasource.id}"}, "fieldConfig": {"defaults": {"color": {"fixedColor": "rgb(31, 120, 193)", "mode": "fixed"}, "mappings": [{"options": {"match": "null", "result": {"text": "N/A"}}, "type": "special"}], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "reqps"}, "overrides": []}, "gridPos": {"h": 4, "w": 6, "x": 0, "y": 1}, "id": 4, "links": [], "maxDataPoints": 100, "options": {"colorMode": "none", "graphMode": "area", "justifyMode": "auto", "orientation": "horizontal", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "textMode": "auto"}, "pluginVersion": "9.3.6", "targets": [{"datasource": {"type": "prometheus", "uid": "${datasource.id}"}, "expr": "round(sum(irate(envoy_http_downstream_rq_total{higress=\"$gateway\"}[5m])), 0.001)", "format": "time_series", "intervalFactor": 1, "refId": "A", "step": 4}], "title": "Downstream Request Volume", "type": "stat"}, {"datasource": {"type": "prometheus", "uid": "${datasource.id}"}, "fieldConfig": {"defaults": {"color": {"fixedColor": "rgb(31, 120, 193)", "mode": "fixed"}, "mappings": [{"options": {"match": "null", "result": {"text": "N/A"}}, "type": "special"}], "thresholds": {"mode": "absolute", "steps": [{"color": "rgba(50, 172, 45, 0.97)", "value": null}, {"color": "rgba(237, 129, 40, 0.89)", "value": 95}, {"color": "rgba(245, 54, 54, 0.9)", "value": 99}]}, "unit": "percentunit"}, "overrides": []}, "gridPos": {"h": 4, "w": 6, "x": 6, "y": 1}, "id": 6, "links": [], "maxDataPoints": 100, "options": {"colorMode": "none", "graphMode": "area", "justifyMode": "auto", "orientation": "horizontal", "reduceOptions": {"calcs": ["mean"], "fields": "", "values": false}, "textMode": "auto"}, "pluginVersion": "9.3.6", "targets": [{"datasource": {"type": "prometheus", "uid": "${datasource.id}"}, "expr": "sum(irate(envoy_http_downstream_rq{higress=\"$gateway\", response_code_class!=\"5xx\"}[5m])) / sum(irate(envoy_http_downstream_rq_total{higress=\"$gateway\"}[5m]))", "format": "time_series", "intervalFactor": 1, "refId": "A"}], "title": "Downstream Success Rate (non-5xx responses)", "type": "stat"}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "${datasource.id}"}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 4, "w": 6, "x": 12, "y": 1}, "hiddenSeries": false, "id": 8, "legend": {"alignAsTable": false, "avg": false, "current": false, "hideEmpty": false, "hideZero": false, "max": false, "min": false, "rightSide": true, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "9.3.6", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": {"type": "prometheus", "uid": "${datasource.id}"}, "expr": "histogram_quantile(0.50, sum(irate(envoy_http_downstream_rq_time_bucket{higress=\"$gateway\"}[1m])) by (le))", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "P50", "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "${datasource.id}"}, "expr": "histogram_quantile(0.90, sum(irate(envoy_http_downstream_rq_time_bucket{higress=\"$gateway\"}[1m])) by (le))", "format": "time_series", "hide": false, "intervalFactor": 1, "legendFormat": "P90", "refId": "B"}, {"datasource": {"type": "prometheus", "uid": "${datasource.id}"}, "expr": "histogram_quantile(0.99, sum(irate(envoy_http_downstream_rq_time_bucket{higress=\"$gateway\"}[1m])) by (le))", "format": "time_series", "hide": false, "intervalFactor": 1, "legendFormat": "P99", "refId": "C"}, {"datasource": {"type": "prometheus", "uid": "${datasource.id}"}, "editorMode": "code", "expr": "sum(irate(envoy_http_downstream_rq_time_sum{higress=\"$gateway\"}[1m])) / sum(irate(envoy_http_downstream_rq_time_count{higress=\"$gateway\"}[1m]))", "hide": false, "interval": "", "legendFormat": "Avg", "range": true, "refId": "D"}], "thresholds": [], "timeRegions": [], "title": "Downstream Request Duration", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:131", "format": "ms", "logBase": 1, "show": true}, {"$$hashKey": "object:132", "format": "short", "logBase": 1, "show": false}], "yaxis": {"align": false}}, {"datasource": {"type": "prometheus", "uid": "${datasource.id}"}, "fieldConfig": {"defaults": {"color": {"fixedColor": "rgb(31, 120, 193)", "mode": "fixed"}, "mappings": [{"options": {"match": "null", "result": {"text": "N/A"}}, "type": "special"}], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "Bps"}, "overrides": []}, "gridPos": {"h": 4, "w": 6, "x": 18, "y": 1}, "id": 10, "links": [], "maxDataPoints": 100, "options": {"colorMode": "none", "graphMode": "area", "justifyMode": "auto", "orientation": "horizontal", "reduceOptions": {"calcs": ["mean"], "fields": "", "values": false}, "textMode": "auto"}, "pluginVersion": "9.3.6", "targets": [{"datasource": {"type": "prometheus", "uid": "${datasource.id}"}, "expr": "sum(irate(envoy_http_downstream_cx_rx_bytes_total{higress=\"$gateway\"}[1m]))", "format": "time_series", "hide": false, "intervalFactor": 1, "legendFormat": "", "refId": "A"}], "title": "TCP Received Bytes", "type": "stat"}, {"datasource": {"type": "prometheus", "uid": "${datasource.id}"}, "fieldConfig": {"defaults": {"color": {"fixedColor": "rgb(31, 120, 193)", "mode": "fixed"}, "mappings": [{"options": {"match": "null", "result": {"text": "N/A"}}, "type": "special"}], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "reqps"}, "overrides": []}, "gridPos": {"h": 4, "w": 6, "x": 0, "y": 5}, "id": 18, "links": [], "maxDataPoints": 100, "options": {"colorMode": "none", "graphMode": "area", "justifyMode": "auto", "orientation": "horizontal", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "textMode": "auto"}, "pluginVersion": "9.3.6", "targets": [{"datasource": {"type": "prometheus", "uid": "${datasource.id}"}, "expr": "round(sum(irate(envoy_cluster_upstream_rq_total{higress=\"$gateway\"}[5m])), 0.001)", "format": "time_series", "intervalFactor": 1, "refId": "A", "step": 4}], "title": "Upstream Request Volume", "type": "stat"}, {"datasource": {"type": "prometheus", "uid": "${datasource.id}"}, "fieldConfig": {"defaults": {"color": {"fixedColor": "rgb(31, 120, 193)", "mode": "fixed"}, "mappings": [{"options": {"match": "null", "result": {"text": "N/A"}}, "type": "special"}], "thresholds": {"mode": "absolute", "steps": [{"color": "rgba(50, 172, 45, 0.97)", "value": null}, {"color": "rgba(237, 129, 40, 0.89)", "value": 95}, {"color": "rgba(245, 54, 54, 0.9)", "value": 99}]}, "unit": "percentunit"}, "overrides": []}, "gridPos": {"h": 4, "w": 6, "x": 6, "y": 5}, "id": 19, "links": [], "maxDataPoints": 100, "options": {"colorMode": "none", "graphMode": "area", "justifyMode": "auto", "orientation": "horizontal", "reduceOptions": {"calcs": ["mean"], "fields": "", "values": false}, "textMode": "auto"}, "pluginVersion": "9.3.6", "targets": [{"datasource": {"type": "prometheus", "uid": "${datasource.id}"}, "expr": "sum(irate(envoy_cluster_upstream_rq{higress=\"$gateway\", cluster_name!~\"\\\\.internal$\",response_code_class!=\"5xx\"}[5m])) / sum(irate(envoy_cluster_upstream_rq{higress=\"$gateway\", cluster_name!~\"\\\\.internal$\"}[5m]))", "format": "time_series", "intervalFactor": 1, "refId": "A"}], "title": "Upstream Success Rate (non-5xx responses)", "type": "stat"}, {"datasource": {"type": "prometheus", "uid": "${datasource.id}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "dtdurationms"}, "overrides": []}, "gridPos": {"h": 4, "w": 6, "x": 12, "y": 5}, "id": 16, "links": [], "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "right", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "9.0.8", "targets": [{"datasource": {"type": "prometheus", "uid": "${datasource.id}"}, "expr": "histogram_quantile(0.50, sum(irate(envoy_cluster_upstream_rq_time_bucket{higress=\"$gateway\"}[1m])) by (le))", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "P50", "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "${datasource.id}"}, "expr": "histogram_quantile(0.90, sum(irate(envoy_cluster_upstream_rq_time_bucket{higress=\"$gateway\"}[1m])) by (le))", "format": "time_series", "hide": false, "intervalFactor": 1, "legendFormat": "P90", "refId": "B"}, {"datasource": {"type": "prometheus", "uid": "${datasource.id}"}, "expr": "histogram_quantile(0.99, sum(irate(envoy_cluster_upstream_rq_time_bucket{higress=\"$gateway\"}[1m])) by (le))", "format": "time_series", "hide": false, "intervalFactor": 1, "legendFormat": "P99", "refId": "C"}, {"datasource": {"type": "prometheus", "uid": "${datasource.id}"}, "editorMode": "code", "expr": "sum(irate(envoy_cluster_upstream_rq_time_sum{higress=\"$gateway\"}[1m])) / sum(irate(envoy_cluster_upstream_rq_time_count{higress=\"$gateway\"}[1m]))", "hide": false, "legendFormat": "Avg", "range": true, "refId": "D"}], "title": "Upstream Request Duration", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${datasource.id}"}, "fieldConfig": {"defaults": {"color": {"fixedColor": "rgb(31, 120, 193)", "mode": "fixed"}, "mappings": [{"options": {"match": "null", "result": {"text": "N/A"}}, "type": "special"}], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "Bps"}, "overrides": []}, "gridPos": {"h": 4, "w": 6, "x": 18, "y": 5}, "id": 12, "links": [], "maxDataPoints": 100, "options": {"colorMode": "none", "graphMode": "area", "justifyMode": "auto", "orientation": "horizontal", "reduceOptions": {"calcs": ["mean"], "fields": "", "values": false}, "textMode": "auto"}, "pluginVersion": "9.3.6", "targets": [{"datasource": {"type": "prometheus", "uid": "${datasource.id}"}, "expr": "sum(irate(envoy_http_downstream_cx_tx_bytes_total{higress=\"$gateway\"}[1m]))", "format": "time_series", "hide": false, "intervalFactor": 1, "legendFormat": "", "refId": "A"}], "title": "TCP Sent Bytes", "type": "stat"}, {"collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 9}, "id": 21, "panels": [], "title": "Workload", "type": "row"}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "${datasource.id}"}, "fieldConfig": {"defaults": {"unit": "percent"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 11, "x": 0, "y": 10}, "hiddenSeries": false, "id": 34, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "9.3.6", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": {"type": "prometheus", "uid": "${datasource.id}"}, "editorMode": "code", "expr": "100 * sum(irate(container_cpu_usage_seconds_total{container=\"higress-gateway\", namespace=\"$namespace\"}[1m]))by(pod)", "format": "time_series", "hide": false, "intervalFactor": 2, "legendFormat": "{{pod}}", "range": true, "refId": "B", "step": 2}], "thresholds": [], "timeRegions": [], "title": "CPU", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:1997", "format": "percent", "logBase": 1, "show": true}, {"$$hashKey": "object:1998", "format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"datasource": {"type": "prometheus", "uid": "${datasource.id}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "bytes"}, "overrides": []}, "gridPos": {"h": 7, "w": 12, "x": 11, "y": 10}, "id": 23, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "${datasource.id}"}, "editorMode": "code", "exemplar": false, "expr": "max(container_memory_working_set_bytes{container=\"higress-gateway\", namespace=\"$namespace\"}) by (pod)", "interval": "", "legendFormat": "{{pod}}-envoy", "range": true, "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "${datasource.id}"}, "editorMode": "code", "expr": "max(container_memory_working_set_bytes{container=\"discovery\", namespace=\"$namespace\"}) by (pod)", "hide": false, "legendFormat": "{{pod}}-istio", "range": true, "refId": "B"}, {"datasource": {"type": "prometheus", "uid": "${datasource.id}"}, "editorMode": "code", "expr": "max(container_memory_working_set_bytes{container=\"higress-core\", namespace=\"$namespace\"}) by (pod)", "hide": false, "legendFormat": "{{pod}}-core", "range": true, "refId": "C"}], "title": "Memory", "type": "timeseries"}, {"collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 17}, "id": 47, "panels": [], "title": "Request", "type": "row"}, {"datasource": {"type": "prometheus", "uid": "${datasource.id}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 11, "x": 0, "y": 18}, "id": 45, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "${datasource.id}"}, "editorMode": "code", "expr": "sum(irate(envoy_http_downstream_rq{higress=\"$gateway\",http_conn_manager_prefix=~\"outbound_0.0.0.0_.*\"}[1m])) by (response_code_class)", "legendFormat": "{{response_code_class}}", "range": true, "refId": "A"}], "title": "Downstream QPS", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${datasource.id}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 11, "y": 18}, "id": 49, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "${datasource.id}"}, "editorMode": "code", "expr": "sum(irate(envoy_cluster_upstream_rq{higress=\"$gateway\",cluster_name=~\"outbound.*\"}[1m])) by (response_code_class)", "legendFormat": "__auto", "range": true, "refId": "A"}], "title": "Upstream QPS", "type": "timeseries"}, {"collapsed": true, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 26}, "id": 52, "panels": [], "title": "WAF", "type": "row"}, {"datasource": {"type": "prometheus", "uid": "${datasource.id}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 8, "x": 0, "y": 27}, "id": 54, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "${datasource.id}"}, "editorMode": "code", "expr": "sum(irate(waf_filter_tx_total{higress=\"$gateway\"}[1m]))", "legendFormat": "qps", "range": true, "refId": "A"}], "title": "WAF Processed", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${datasource.id}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 8, "x": 8, "y": 27}, "id": 55, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "${datasource.id}"}, "editorMode": "code", "expr": "sum(irate(waf_filter_tx_interruptions{higress=\"$gateway\"}[1m])) by (phase)", "legendFormat": "{{phase}}", "range": true, "refId": "A"}], "title": "WAF Denied by Phase", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${datasource.id}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 8, "x": 16, "y": 27}, "id": 56, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "${datasource.id}"}, "editorMode": "code", "expr": "sum(irate(waf_filter_tx_interruptions{higress=\"$gateway\"}[1m])) by (ruleid)", "legendFormat": "{{ruleid}}", "range": true, "refId": "A"}], "title": "WAF Denied by RuleID", "type": "timeseries"}, {"collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 35}, "id": 26, "panels": [], "title": "XDS", "type": "row"}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "${datasource.id}"}, "description": "Shows details about Envoy proxies in the mesh", "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 8, "x": 0, "y": 36}, "hiddenSeries": false, "id": 28, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "9.3.6", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": {"type": "prometheus", "uid": "${datasource.id}"}, "expr": "sum(irate(envoy_cluster_upstream_cx_total{cluster_name=\"xds-grpc\"}[1m]))", "format": "time_series", "hide": false, "intervalFactor": 1, "legendFormat": "XDS Connections", "refId": "C"}, {"datasource": {"type": "prometheus", "uid": "${datasource.id}"}, "expr": "sum(irate(envoy_cluster_upstream_cx_connect_fail{cluster_name=\"xds-grpc\"}[1m]))", "format": "time_series", "hide": false, "intervalFactor": 1, "legendFormat": "XDS Connection Failures", "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "${datasource.id}"}, "expr": "sum(increase(envoy_server_hot_restart_epoch[1m]))", "format": "time_series", "intervalFactor": 1, "legendFormat": "Envoy <PERSON><PERSON>s", "refId": "B"}], "thresholds": [], "timeRegions": [], "title": "Envoy Details", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:2269", "format": "ops", "logBase": 1, "show": true}, {"$$hashKey": "object:2270", "format": "ops", "logBase": 1, "show": false}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "${datasource.id}"}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 8, "x": 8, "y": 36}, "hiddenSeries": false, "id": 30, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "9.3.6", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": {"type": "prometheus", "uid": "${datasource.id}"}, "expr": "sum(envoy_cluster_upstream_cx_active{cluster_name=\"xds-grpc\"})", "format": "time_series", "intervalFactor": 2, "legendFormat": "XDS Active Connections", "refId": "C", "step": 2}], "thresholds": [], "timeRegions": [], "title": "XDS Active Connections", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:309", "format": "short", "logBase": 1, "show": true}, {"$$hashKey": "object:310", "format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "${datasource.id}"}, "description": "Shows the size of XDS requests and responses", "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 8, "x": 16, "y": 36}, "hiddenSeries": false, "id": 32, "legend": {"avg": false, "current": false, "hideEmpty": false, "hideZero": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "9.3.6", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": {"type": "prometheus", "uid": "${datasource.id}"}, "expr": "max(rate(envoy_cluster_upstream_cx_rx_bytes_total{cluster_name=\"xds-grpc\"}[1m]))", "format": "time_series", "hide": false, "intervalFactor": 1, "legendFormat": "XDS Response Bytes Max", "refId": "D"}, {"datasource": {"type": "prometheus", "uid": "${datasource.id}"}, "expr": "quantile(0.5, rate(envoy_cluster_upstream_cx_rx_bytes_total{cluster_name=\"xds-grpc\"}[1m]))", "format": "time_series", "hide": false, "intervalFactor": 1, "legendFormat": "XDS Response Bytes Average", "refId": "B"}, {"datasource": {"type": "prometheus", "uid": "${datasource.id}"}, "expr": "max(rate(envoy_cluster_upstream_cx_tx_bytes_total{cluster_name=\"xds-grpc\"}[1m]))", "format": "time_series", "intervalFactor": 1, "legendFormat": "XDS Request Bytes Max", "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "${datasource.id}"}, "expr": "quantile(.5, rate(envoy_cluster_upstream_cx_tx_bytes_total{cluster_name=\"xds-grpc\"}[1m]))", "format": "time_series", "intervalFactor": 1, "legendFormat": "XDS Request Bytes Average", "refId": "C"}], "thresholds": [], "timeRegions": [], "title": "XDS Requests Size", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:398", "format": "Bps", "logBase": 1, "show": true}, {"$$hashKey": "object:399", "format": "ops", "logBase": 1, "show": false}], "yaxis": {"align": false}}, {"collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 44}, "id": 38, "panels": [], "title": "Service Top", "type": "row"}, {"datasource": {"type": "prometheus", "uid": "${datasource.id}"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": "auto", "displayMode": "auto", "inspect": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 45}, "id": 40, "options": {"footer": {"fields": "", "reducer": ["sum"], "show": false}, "showHeader": true}, "pluginVersion": "9.3.6", "targets": [{"datasource": {"type": "prometheus", "uid": "${datasource.id}"}, "editorMode": "code", "exemplar": false, "expr": "label_replace(label_replace(topk(10, sum(irate(envoy_cluster_upstream_rq_total{higress=\"$gateway\", cluster_name!=\"xds-grpc\", cluster_name!=\"prometheus_stats\", cluster_name!=\"agent\", cluster_name!=\"BlackHoleCluster\", cluster_name!=\"sds-grpc\"}[5m])) by (cluster_name)), \"service\", \"$3\", \"cluster_name\", \"outbound_([0-9]+)_(.*)_(.*)$\"), \"port\", \"$1\", \"cluster_name\", \"outbound_([0-9]+)_(.*)_(.*)$\")", "format": "table", "instant": true, "interval": "", "legendFormat": "__auto", "range": false, "refId": "A"}], "title": "QPS Top 10", "transformations": [{"id": "filterFieldsByName", "options": {"include": {"pattern": ""}}}, {"id": "organize", "options": {"excludeByName": {}, "indexByName": {"Value": 2, "port": 1, "service": 0}, "renameByName": {"Value": "QPS"}}}], "type": "table"}, {"datasource": {"type": "prometheus", "uid": "${datasource.id}"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": "auto", "displayMode": "auto", "inspect": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 45}, "id": 50, "options": {"footer": {"fields": "", "reducer": ["sum"], "show": false}, "showHeader": true}, "pluginVersion": "9.3.6", "targets": [{"datasource": {"type": "prometheus", "uid": "${datasource.id}"}, "editorMode": "code", "exemplar": false, "expr": "label_replace(label_replace(topk(10, sum(irate(envoy_cluster_upstream_rq{response_code_class=~\"(4|5)xx\",higress=\"$gateway\", cluster_name!=\"xds-grpc\", cluster_name!=\"prometheus_stats\", cluster_name!=\"agent\", cluster_name!=\"BlackHoleCluster\", cluster_name!=\"sds-grpc\"}[5m])) by (cluster_name)), \"service\", \"$3\", \"cluster_name\", \"outbound_([0-9]+)_(.*)_(.*)$\"), \"port\", \"$1\", \"cluster_name\", \"outbound_([0-9]+)_(.*)_(.*)$\")", "format": "table", "instant": true, "interval": "", "legendFormat": "__auto", "range": false, "refId": "A"}], "title": "Failure Requests Top 10", "transformations": [{"id": "filterFieldsByName", "options": {"include": {"pattern": ""}}}, {"id": "organize", "options": {"excludeByName": {}, "indexByName": {"Value": 2, "port": 1, "service": 0}, "renameByName": {"Value": "QPS"}}}], "type": "table"}, {"datasource": {"type": "prometheus", "uid": "${datasource.id}"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": "auto", "displayMode": "auto", "inspect": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 53}, "id": 41, "options": {"footer": {"fields": "", "reducer": ["sum"], "show": false}, "showHeader": true}, "pluginVersion": "9.3.6", "targets": [{"datasource": {"type": "prometheus", "uid": "${datasource.id}"}, "editorMode": "code", "exemplar": false, "expr": "label_replace(label_replace(topk(10, sum(envoy_cluster_upstream_cx_active{higress=\"$gateway\", cluster_name!=\"xds-grpc\", cluster_name!=\"prometheus_stats\", cluster_name!=\"agent\", cluster_name!=\"BlackHoleCluster\", cluster_name!=\"sds-grpc\"}) by (cluster_name)), \"service\", \"$3\", \"cluster_name\", \"outbound_([0-9]+)_(.*)_(.*)$\"), \"port\", \"$1\", \"cluster_name\", \"outbound_([0-9]+)_(.*)_(.*)$\")", "format": "table", "instant": true, "interval": "", "legendFormat": "__auto", "range": false, "refId": "A"}], "title": "Active Connection Top 10", "transformations": [{"id": "filterFieldsByName", "options": {"include": {"pattern": ""}}}, {"id": "organize", "options": {"excludeByName": {}, "indexByName": {"Value": 2, "port": 1, "service": 0}, "renameByName": {"Value": "Connections", "cluster_name": ""}}}], "type": "table"}, {"datasource": {"type": "prometheus", "uid": "${datasource.id}"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": "auto", "displayMode": "auto", "inspect": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 53}, "id": 42, "options": {"footer": {"fields": "", "reducer": ["sum"], "show": false}, "showHeader": true}, "pluginVersion": "9.3.6", "targets": [{"datasource": {"type": "prometheus", "uid": "${datasource.id}"}, "editorMode": "code", "exemplar": false, "expr": "label_replace(label_replace(topk(10, sum(irate(envoy_cluster_upstream_rq_time_sum{higress=\"$gateway\", cluster_name!=\"xds-grpc\", cluster_name!=\"prometheus_stats\", cluster_name!=\"agent\", cluster_name!=\"BlackHoleCluster\", cluster_name!=\"sds-grpc\"}[5m])) by (cluster_name) / sum(irate(envoy_cluster_upstream_rq_time_count{higress=\"$gateway\",cluster_name!=\"xds-grpc\", cluster_name!=\"prometheus_stats\", cluster_name!=\"agent\", cluster_name!=\"BlackHoleCluster\", cluster_name!=\"sds-grpc\"}[5m])) by (cluster_name)), \"service\", \"$3\", \"cluster_name\", \"outbound_([0-9]+)_(.*)_(.*)$\"), \"port\", \"$1\", \"cluster_name\", \"outbound_([0-9]+)_(.*)_(.*)$\")", "format": "table", "instant": true, "interval": "", "legendFormat": "__auto", "range": false, "refId": "A"}], "title": "RT Top 10(Slow)", "transformations": [{"id": "filterFieldsByName", "options": {"include": {"pattern": ""}}}, {"id": "organize", "options": {"excludeByName": {}, "indexByName": {"Value": 2, "port": 1, "service": 0}, "renameByName": {"Value": "RT", "service": ""}}}], "type": "table"}], "schemaVersion": 37, "style": "dark", "tags": [], "templating": {"list": [{"current": {"selected": false, "text": "higress-system-higress-gateway", "value": "higress-system-higress-gateway"}, "datasource": {"type": "prometheus", "uid": "${datasource.id}"}, "definition": "label_values(envoy_server_live, higress)", "hide": 0, "includeAll": false, "multi": false, "name": "gateway", "options": [], "query": {"query": "label_values(envoy_server_live, higress)", "refId": "StandardVariableQuery"}, "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "type": "query"}, {"current": {"selected": false, "text": "higress-system", "value": "higress-system"}, "datasource": {"type": "prometheus", "uid": "${datasource.id}"}, "definition": "label_values(envoy_server_live, higress)", "hide": 0, "includeAll": false, "multi": false, "name": "namespace", "options": [], "query": {"query": "label_values(envoy_server_live, higress)", "refId": "StandardVariableQuery"}, "refresh": 1, "regex": "/(.*)-higress-gateway/", "skipUrlSync": false, "sort": 0, "type": "query"}]}, "time": {"from": "now-5m", "to": "now"}, "timepicker": {}, "timezone": "", "title": "Higress Dashboard", "weekStart": ""}