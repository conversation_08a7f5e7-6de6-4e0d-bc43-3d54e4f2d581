#
# Copyright (c) 2022-2023 Alibaba Group Holding Ltd.
#
# Licensed under the Apache License, Version 2.0 (the "License"); you may not use this file except in compliance with
# the License. You may obtain a copy of the License at
#
# http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software distributed under the License is distributed on
# an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the License for the
# specific language governing permissions and limitations under the License.
#

springdoc.api-docs.enabled=true
springdoc.pathsToMatch=/v1/**,/session/**,/dashboard/**,/system/**,/user/**
springdoc.swagger-ui.enabled=true
springdoc.swagger-ui.operationsSorter=alpha
springdoc.swagger-ui.tagsSorter=alpha

higress-console.build.version=@app.build.version@
higress-console.build.dev=@app.build.dev@

server.compression.enabled=true
server.compression.mime-types=application/json,application/xml,text/html,text/xml,text/plain,text/css,text/javascript,application/javascript
server.compression.min-response-size=2048
server.compression.excluded-user-agents=MSIE 6.0,UCBrowser

# Add %mdc (Mapped Diagnostic Context) based on the Spring Boot default logging pattern found in org.springframework.boot.logging.logback.DefaultLogbackConfiguration
logging.pattern.console=%clr(%d{yyyy-MM-dd HH:mm:ss.SSS}){faint} %clr(%5p) %clr(70396){magenta} %clr(---){faint} %clr([%15.15t]){faint} %clr(%-40.40logger{39}){cyan} {%mdc} %clr(:){faint} %m%n%wEx