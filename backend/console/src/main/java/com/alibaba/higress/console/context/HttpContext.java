/*
 * Copyright (c) 2022-2023 Alibaba Group Holding Ltd.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not use this file except in compliance with
 * the License. You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software distributed under the License is distributed on
 * an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the License for the
 * specific language governing permissions and limitations under the License.
 */
package com.alibaba.higress.console.context;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

public class HttpContext {

    private static final ThreadLocal<HttpContext> INSTANCE = new ThreadLocal<>();

    private final HttpServletRequest request;
    private final HttpServletResponse response;

    private HttpContext(HttpServletRequest request, HttpServletResponse response) {
        this.request = request;
        this.response = response;
    }

    public static HttpContext init(HttpServletRequest request, HttpServletResponse response) {
        HttpContext context = new HttpContext(request, response);
        INSTANCE.set(context);
        return context;
    }

    public static HttpContext getCurrent() {
        return INSTANCE.get();
    }

    public static void release() {
        INSTANCE.remove();
    }

    public HttpServletRequest getRequest() {
        return request;
    }

    public HttpServletResponse getResponse() {
        return response;
    }
}
