<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>

	<parent>
		<groupId>org.springframework.boot</groupId>
		<artifactId>spring-boot-starter-parent</artifactId>
		<version>2.7.18</version>
		<relativePath/> <!-- lookup parent from repository -->
	</parent>

	<groupId>io.higress.api</groupId>
	<artifactId>higress-admin-parent</artifactId>
	<version>0.0.1-SNAPSHOT</version>
	<name>higress-admin-parent</name>
	<description>Admin Project for Higress</description>
	<url>http://higress.io</url>

	<packaging>pom</packaging>

	<modules>
		<module>sdk</module>
		<module>console</module>
	</modules>

	<licenses>
		<license>
			<name>The Apache License, Version 2.0</name>
			<url>http://www.apache.org/licenses/LICENSE-2.0.txt</url>
			<distribution>repo</distribution>
		</license>
	</licenses>

	<scm>
		<url>**************:alibaba/higress.git</url>
		<connection>scm:**************:alibaba/higress.git</connection>
		<developerConnection>scm:**************:alibaba/higress.git</developerConnection>
	</scm>

	<developers>
		<developer>
			<name>johnlanni</name>
			<email><EMAIL></email>
		</developer>
		<developer>
			<name>CH3CHO</name>
			<email><EMAIL></email>
		</developer>
	</developers>

	<properties>
		<java.version>17</java.version>

		<retrofit.version>2.9.0</retrofit.version>
		<guava.version>31.1-jre</guava.version>
		<commons-lang3.version>3.13.0</commons-lang3.version>
		<commons-collection4.version>4.4</commons-collection4.version>
		<fastjson.version>1.2.83</fastjson.version>
		<!-- https://github.com/kubernetes-client/java/wiki/2.-Versioning-and-Compatibility -->
		<k8s-client.version>17.0.0</k8s-client.version>
		<bouncycastle.version>1.46</bouncycastle.version>

		<pmd-plugin.version>3.20.0</pmd-plugin.version>
		<frontend-plugin.version>1.12.1</frontend-plugin.version>
		<git-commit-id-plugin.version>6.0.0</git-commit-id-plugin.version>
		<license-plugin.version>4.1</license-plugin.version>
		<checkstyle-plugin.version>3.2.1</checkstyle-plugin.version>
		<maven-gpg-plugin.version>3.1.0</maven-gpg-plugin.version>
		<central-publishing-maven-plugin.version>0.3.0</central-publishing-maven-plugin.version>
		<lombok-maven-plugin.version>*********</lombok-maven-plugin.version>
		<swagger-core.version>2.2.20</swagger-core.version>
		<springdoc.version>1.8.0</springdoc.version>

		<plugin.inputEncoding>${project.build.sourceEncoding}</plugin.inputEncoding>
		<plugin.outputEncoding>${project.build.sourceEncoding}</plugin.outputEncoding>

		<delombok.output>${project.basedir}/target/delombok-sources</delombok.output>

		<gpg.sign.skip>false</gpg.sign.skip>
	</properties>

	<dependencyManagement>
		<dependencies>
			<dependency>
				<groupId>com.google.guava</groupId>
				<artifactId>guava</artifactId>
				<version>${guava.version}</version>
			</dependency>
			<dependency>
				<groupId>org.apache.commons</groupId>
				<artifactId>commons-lang3</artifactId>
				<version>${commons-lang3.version}</version>
			</dependency>
			<dependency>
				<groupId>org.apache.commons</groupId>
				<artifactId>commons-collections4</artifactId>
				<version>${commons-collection4.version}</version>
			</dependency>
			<dependency>
				<groupId>com.alibaba</groupId>
				<artifactId>fastjson</artifactId>
				<version>${fastjson.version}</version>
			</dependency>
			<dependency>
				<groupId>io.kubernetes</groupId>
				<artifactId>client-java</artifactId>
				<version>${k8s-client.version}</version>
			</dependency>
			<dependency>
				<groupId>org.bouncycastle</groupId>
				<artifactId>bcprov-jdk16</artifactId>
				<version>${bouncycastle.version}</version>
			</dependency>
			<dependency>
				<groupId>com.squareup.retrofit2</groupId>
				<artifactId>retrofit</artifactId>
				<version>${retrofit.version}</version>
			</dependency>
			<dependency>
				<groupId>com.squareup.retrofit2</groupId>
				<artifactId>converter-jackson</artifactId>
				<version>${retrofit.version}</version>
			</dependency>
			<dependency>
				<groupId>org.springdoc</groupId>
				<artifactId>springdoc-openapi-ui</artifactId>
				<version>${springdoc.version}</version>
			</dependency>
			<dependency>
				<groupId>io.swagger.core.v3</groupId>
				<artifactId>swagger-core</artifactId>
				<version>${swagger-core.version}</version>
			</dependency>
		</dependencies>
	</dependencyManagement>

	<build>
		<pluginManagement>
			<plugins>
				<plugin>
					<groupId>org.apache.maven.plugins</groupId>
					<artifactId>maven-pmd-plugin</artifactId>
					<version>${pmd-plugin.version}</version>
				</plugin>
				<plugin>
					<groupId>com.github.eirslett</groupId>
					<artifactId>frontend-maven-plugin</artifactId>
					<version>${frontend-plugin.version}</version>
				</plugin>
				<plugin>
					<groupId>io.github.git-commit-id</groupId>
					<artifactId>git-commit-id-maven-plugin</artifactId>
					<version>${git-commit-id-plugin.version}</version>
				</plugin>
				<plugin>
					<groupId>org.apache.maven.plugins</groupId>
					<artifactId>maven-gpg-plugin</artifactId>
					<version>${maven-gpg-plugin.version}</version>
				</plugin>
				<plugin>
					<groupId>org.sonatype.central</groupId>
					<artifactId>central-publishing-maven-plugin</artifactId>
					<version>${central-publishing-maven-plugin.version}</version>
					<extensions>true</extensions>
				</plugin>
			</plugins>
		</pluginManagement>
		<plugins>
			<plugin>
				<artifactId>maven-compiler-plugin</artifactId>
				<configuration>
					<source>${maven.compiler.source}</source>
					<target>${maven.compiler.target}</target>
					<compilerVersion>${maven.compiler.source}</compilerVersion>
					<showDeprecation>true</showDeprecation>
					<showWarnings>true</showWarnings>
				</configuration>
			</plugin>
			<plugin>
				<groupId>org.projectlombok</groupId>
				<artifactId>lombok-maven-plugin</artifactId>
				<version>${lombok-maven-plugin.version}</version>
				<configuration>
					<sourceDirectory>${project.basedir}/src/main/java</sourceDirectory>
					<outputDirectory>${delombok.output}</outputDirectory>
					<addOutputDirectory>false</addOutputDirectory>
				</configuration>
				<executions>
					<execution>
						<phase>generate-sources</phase>
						<goals>
							<goal>delombok</goal>
						</goals>
					</execution>
				</executions>
				<dependencies>
					<dependency>
						<groupId>org.projectlombok</groupId>
						<artifactId>lombok</artifactId>
						<version>${lombok.version}</version>
					</dependency>
				</dependencies>
			</plugin>
			<plugin>
				<artifactId>maven-javadoc-plugin</artifactId>
				<configuration>
					<charset>UTF-8</charset>
					<sourcepath>${delombok.output}</sourcepath>
				</configuration>
				<executions>
					<execution>
						<id>attach-javadocs</id>
						<goals>
							<goal>jar</goal>
						</goals>
					</execution>
				</executions>
			</plugin>
			<plugin>
				<artifactId>maven-source-plugin</artifactId>
				<executions>
					<execution>
						<id>attach-sources</id>
						<goals>
							<goal>jar</goal>
						</goals>
					</execution>
				</executions>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-pmd-plugin</artifactId>
				<configuration>
					<inputEncoding>${plugin.inputEncoding}</inputEncoding>
					<outputEncoding>${plugin.outputEncoding}</outputEncoding>
					<targetJdk>${maven.compiler.target}</targetJdk>
					<printFailingErrors>true</printFailingErrors>
					<failurePriority>2</failurePriority>
					<verbose>true</verbose>
					<rulesets>
						<ruleset>rulesets/java/ali-comment.xml</ruleset>
						<ruleset>rulesets/java/ali-concurrent.xml</ruleset>
						<ruleset>rulesets/java/ali-constant.xml</ruleset>
						<ruleset>rulesets/java/ali-exception.xml</ruleset>
						<ruleset>rulesets/java/ali-flowcontrol.xml</ruleset>
						<ruleset>rulesets/java/ali-naming.xml</ruleset>
						<ruleset>rulesets/java/ali-oop.xml</ruleset>
						<ruleset>rulesets/java/ali-orm.xml</ruleset>
						<ruleset>rulesets/java/ali-other.xml</ruleset>
						<ruleset>rulesets/java/ali-set.xml</ruleset>
					</rulesets>
					<excludes>
						<exclude>**/FixClassTypeResolver.java</exclude>
					</excludes>
				</configuration>
				<executions>
					<execution>
						<phase>validate</phase>
						<goals>
							<goal>check</goal>
						</goals>
					</execution>
				</executions>
				<dependencies>
					<dependency>
						<groupId>com.alibaba.p3c</groupId>
						<artifactId>p3c-pmd</artifactId>
						<version>2.1.1</version>
					</dependency>
				</dependencies>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-gpg-plugin</artifactId>
				<executions>
					<execution>
						<id>sign-artifacts</id>
						<phase>verify</phase>
						<goals>
							<goal>sign</goal>
						</goals>
						<configuration>
							<skip>${gpg.sign.skip}</skip>
						</configuration>
					</execution>
				</executions>
			</plugin>
			<plugin>
				<groupId>org.sonatype.central</groupId>
				<artifactId>central-publishing-maven-plugin</artifactId>
				<extensions>true</extensions>
				<configuration>
					<publishingServerId>central</publishingServerId>
					<tokenAuth>true</tokenAuth>
					<excludeArtifacts>
						<artifact>higress-console</artifact>
					</excludeArtifacts>
				</configuration>
			</plugin>
		</plugins>
	</build>

	<profiles>
		<profile>
			<id>license</id>
			<properties>
				<checkstyle.skip>true</checkstyle.skip>
			</properties>
			<build>
				<plugins>
					<plugin>
						<groupId>com.mycila</groupId>
						<artifactId>license-maven-plugin</artifactId>
						<version>${license-plugin.version}</version>
						<executions>
							<execution>
								<phase>generate-sources</phase>
								<goals>
									<goal>remove</goal>
									<goal>format</goal>
								</goals>
							</execution>
						</executions>
						<configuration>
							<quiet>true</quiet>
							<header>${session.executionRootDirectory}/style/copyright</header>
							<includes>
								<include>**/src/main/java/**</include>
								<include>**/src/main/resources/**</include>
								<include>**/src/test/java/**</include>
								<include>**/src/test/resources/**</include>
								<include>**/style/*.xml</include>
							</includes>
							<excludes>
								<include>**/src/main/resources/static/**</include>
								<include>**/src/main/resources/plugins/**</include>
								<exclude>**/generated/**</exclude>
							</excludes>
							<strictCheck>true</strictCheck>
							<mapping>
								<java>SLASHSTAR_STYLE</java>
								<properties>SCRIPT_STYLE</properties>
								<xml>XML_STYLE</xml>
							</mapping>
						</configuration>
					</plugin>
				</plugins>
			</build>
		</profile>
		<profile>
			<id>checkstyle</id>
			<activation>
				<jdk>[1.8,)</jdk>
			</activation>
			<build>
				<plugins>
					<plugin>
						<groupId>org.apache.maven.plugins</groupId>
						<artifactId>maven-checkstyle-plugin</artifactId>
						<version>${checkstyle-plugin.version}</version>
						<executions>
							<execution>
								<id>validate</id>
								<phase>compile</phase>
								<configuration>
									<configLocation>${session.executionRootDirectory}/style/higress_checkstyle.xml
									</configLocation>
									<suppressionsLocation>
										${session.executionRootDirectory}/style/higress_suppressions.xml
									</suppressionsLocation>
									<inputEncoding>${plugin.inputEncoding}</inputEncoding>
									<outputEncoding>${plugin.outputEncoding}</outputEncoding>
									<consoleOutput>true</consoleOutput>
									<failsOnError>true</failsOnError>
								</configuration>
								<goals>
									<goal>checkstyle</goal>
								</goals>
							</execution>
						</executions>
					</plugin>
				</plugins>
			</build>
		</profile>
		<profile>
			<id>api-docs</id>
			<properties>
				<maven.test.skip>true</maven.test.skip>
				<enforcer.skip>true</enforcer.skip>
				<gpg.sign.skip>true</gpg.sign.skip>
				<skip.npm>true</skip.npm>
				<skip.npx>true</skip.npx>
				<lombok.delombok.skip>true</lombok.delombok.skip>
				<checkstyle.skip>true</checkstyle.skip>
				<pmd.skip>true</pmd.skip>
			</properties>
		</profile>
	</profiles>
</project>
