apiVersion: v1
kind: Service
metadata:
  name: {{ include "higress-console.name" . }}
  namespace: {{ .Release.Namespace }}
  labels:
    {{- include "higress-console.labels" . | nindent 4 }}
spec:
  type: {{ .Values.service.type }}
  ports:
    - port: {{ .Values.service.port }}
      targetPort: http
      protocol: TCP
      name: http
  selector:
    {{- include "higress-console.selectorLabels" . | nindent 4 }}
