{{- $existedSecret := (lookup "v1" "Secret" .Release.Namespace (include "higress-console.name" .)) }}
{{- $password := .Values.admin.password }}
apiVersion: v1
kind: Secret
metadata:
  name: {{ include "higress-console.name" . }}
  namespace: {{ .Release.Namespace }}
type: Opaque
data:
{{- if $existedSecret }}
  {{- range $k, $v := $existedSecret.data }}
  {{ $k }}: {{ $v }}
  {{- end}}
{{- else if $password }}
  # Only initialize the secret if user sets the password explictly.
  adminUsername: {{ .Values.admin.username | b64enc }} 
  adminDisplayName: {{ .Values.admin.displayName | b64enc }} 
  adminPassword: {{ $password | b64enc }}
  key: {{ randAscii 32 | b64enc }}
  iv: {{ randAscii 16 | b64enc }}
{{- end}}
