apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: {{ include "higress-console.name" . }}
  namespace: {{ .Release.Namespace }}
  labels:
    {{- include "higress-console.labels" . | nindent 4 }}
rules:
- apiGroups: [""]
  resources: ["secrets"]
  verbs: ["create", "get", "list", "watch", "update", "delete"]
- apiGroups: [""]
  resources: ["configmaps"]
  verbs: ["create", "get", "list", "watch", "update", "delete"]
---
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: {{ include "higress-console-promtail.name" . }}
  namespace: {{ .Release.Namespace }}
rules:
- apiGroups: [""]
  resources: ["pods"]
  verbs: ["get", "list", "watch"]
