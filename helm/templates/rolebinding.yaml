apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: {{ include "higress-console.name" . }}
  namespace: {{ .Release.Namespace }}
  labels:
    {{- include "higress-console.labels" . | nindent 4 }}
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: Role
  name: {{ include "higress-console.name" . }}
subjects:
  - kind: ServiceAccount
    name: {{ include "higress-console.name" . }}
    namespace: {{ .Release.Namespace }}
---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: {{ include "higress-console-promtail.name" . }}
  namespace: {{ .Release.Namespace }}
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: Role
  name: {{ include "higress-console-promtail.name" . }}
subjects:
  - kind: ServiceAccount
    name: {{ include "higress-console-promtail.name" . }}
    namespace: {{ .Release.Namespace }}
