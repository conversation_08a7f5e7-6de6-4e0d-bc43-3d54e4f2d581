// eslint-disable-next-line max-len
export const DEFAULT_PLUGIN_IMG = `data:image/png;base64,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`;

export const BUILTIN_ROUTE_PLUGIN_LIST = [
  {
    key: 'rewrite',
    category: 'route',
    title: '重写',
    'x-title-i18n': {
      'en-US': 'Rewrite',
    },
    description: '修改请求的域名（Host）以及请求路径（Path），通常用于后端服务的域名/路由与网关侧域名/路由不一致时的配置',
    'x-description-i18n': {
      'en-US': 'Update the Host and Path field in the request. '
        + 'Usually used when the host or path of the upstream is different from the one configured in the gateway.',
    },
    icon: '',
    builtIn: true,
  },
  {
    key: 'headerModify',
    category: 'route',
    title: 'Header设置',
    'x-title-i18n': {
      'en-US': 'Header Control',
    },
    description: '支持增加/删除/修改 HTTP 请求头以及 HTTP 应答头',
    'x-description-i18n': {
      'en-US': 'Support adding/deleting/modifying HTTP request and response headers.',
    },
    icon: '',
    builtIn: true,
  },
  {
    key: 'cors',
    category: 'route',
    title: '跨域',
    'x-title-i18n': {
      'en-US': 'CORS',
    },
    description: '通过配置标示除了当前站点以外的其他源（域名、协议或端口），使得浏览器允许这些源访问加载该路由的响应',
    'x-description-i18n': {
      'en-US': 'Add marks to origins other than the current website (host, protocol or port), '
        + 'so browsers can allow these origins to load corresponding responses.',
    },
    icon: '',
    builtIn: true,
  },
  {
    key: 'retries',
    category: 'route',
    title: '重试',
    'x-title-i18n': {
      'en-US': 'Retries',
    },
    description: '配置网关向后端服务请求当前路由的响应失败时的重试机制',
    'x-description-i18n': {
      'en-US': 'Configure the retry mechanism used by gateway when the given route returns an error.',
    },
    icon: '',
    builtIn: true,
  },
];
