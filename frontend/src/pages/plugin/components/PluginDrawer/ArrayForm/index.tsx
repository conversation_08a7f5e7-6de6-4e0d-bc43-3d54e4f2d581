import { DeleteOutlined, PlusOutlined } from '@ant-design/icons';
import { Button, Form, Input, Select, Table } from 'antd';
import type { FormInstance } from 'antd/es/form';
import { uniqueId } from 'lodash';
import React, { useContext, useEffect, useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';
import styles from './index.module.css';
import i18next from 'i18next';

const EditableContext = React.createContext<FormInstance<any> | null>(null);

const PRIMITIVE_VALUE_DATA_INDEX = "Item";

interface Item {
  key: string;
  data: any;
}

interface EditableRowProps {
  index: number;
}

const EditableRow: React.FC<EditableRowProps> = ({ index, ...props }) => {
  const [form] = Form.useForm();
  return (
    <Form form={form} component={false}>
      <EditableContext.Provider value={form}>
        <tr {...props} />
      </EditableContext.Provider>
    </Form>
  );
};

interface EditableCellProps {
  title: React.ReactNode;
  editable: boolean;
  children: React.ReactNode;
  dataIndex: keyof Item;
  record: Item;
  nodeType: string;
  required: boolean;
  handleSave: (record: Item, valid: boolean) => void;
}

const EditableCell: React.FC<EditableCellProps> = ({
  title,
  editable,
  children,
  dataIndex,
  nodeType,
  record,
  required,
  handleSave,
  ...restProps
}) => {
  const { t } = useTranslation();
  const [editing, setEditing] = useState(true);
  const inputRef = useRef(null);
  const form = useContext(EditableContext)!;

  const matchOptions = ['PRE', 'EQUAL', 'REGULAR'].map((v) => {
    return { label: t(`route.matchTypes.${v}`), value: v };
  });

  useEffect(() => {
    if (record && record.data != null && record.data !== "") {
      if (typeof record.data === 'object') {
        form.setFieldsValue({ ...record.data });
      } else {
        form.setFieldValue(PRIMITIVE_VALUE_DATA_INDEX, record.data);
      }
    }
  }, [editing, record]);

  const save = async () => {
    form.validateFields().then(values => {
      handleSave({ ...record, data: values }, true);
    }).catch(e => {
      handleSave({ ...record, data: form.getFieldsValue() }, false);
    });
  };

  let childNode = children;
  let node;

  const handleInputChange = (name, value) => {
    form.setFieldValue(name, value);
  };

  switch (nodeType) {
    case 'string':
      node = (
        <Input
          ref={inputRef}
          onPressEnter={save}
          onBlur={save}
          onChange={(e) => handleInputChange(dataIndex, e.target.value)}
        />
      );
      break;
    case 'integer':
      node = (
        <Input
          type="number"
          ref={inputRef}
          onPressEnter={save}
          onBlur={save}
          onChange={(e) => handleInputChange(dataIndex, parseInt(e.target.value, 10))}
        />
      );
      break;
    case 'number':
      node = (
        <Input
          type="number"
          step="any"
          ref={inputRef}
          onPressEnter={save}
          onBlur={save}
          onChange={(e) => handleInputChange(dataIndex, parseFloat(e.target.value))}
        />
      );
      break;
    case 'boolean':
      node = (
        <Select ref={inputRef} onBlur={save}>
          <Select.Option value>true</Select.Option>
          <Select.Option value={false}>false</Select.Option>
        </Select>
      );
      break;
    default:
      node = (
        <Input
          ref={inputRef}
          onPressEnter={save}
          onBlur={save}
          onChange={(e) => handleInputChange(dataIndex, e.target.value)}
        />
      );
  }

  if (editable) {
    childNode = (
      <Form.Item
        style={{ margin: 0 }}
        name={dataIndex}
        rules={[
          {
            required,
            message: `${title} ${t('misc.isRequired')}`,
          },
        ]}
      >
        {node}
      </Form.Item>
    );
  }

  return <td {...restProps}>{childNode}</td>;
};

type EditableTableProps = Parameters<typeof Table>[0];

interface DataType {
  uid: number;
  new: boolean;
  invalid: boolean;
  data: any;
}

type ColumnTypes = Exclude<EditableTableProps['columns'], undefined>;

const ArrayForm: React.FC = ({ array, value, onChange }) => {
  const { t } = useTranslation();

  const initDataSource = value || [];
  for (const item of initDataSource) {
    if (!item.uid) {
      item.uid = uniqueId();
    }
  }

  const [dataSource, setDataSource] = useState<DataType[]>(initDataSource);

  function getLocalizedText(obj: any, index: string, defaultText: string) {
    const i18nObj = obj[`x-${index}-i18n`];
    return i18nObj && i18nObj[i18next.language] || obj[index] || defaultText || '';
  }

  const defaultColumns: any[] = [];
  if (array.type === 'object') {
    Object.entries(array.properties).forEach(([key, prop]) => {
      let translatedTitle = getLocalizedText(prop, 'title', key);
      const isRequired = (array.required || []).includes(key);
      defaultColumns.push({
        title: translatedTitle,
        dataIndex: key,
        editable: true,
        required: isRequired,
        nodeType: prop.type,
      });
    });
  } else {
    let translatedTitle = getLocalizedText(array, 'title', '');
    defaultColumns.push({
      title: translatedTitle,
      dataIndex: PRIMITIVE_VALUE_DATA_INDEX,
      editable: true,
      required: true,
      nodeType: array.type,
    });
  }

  defaultColumns.push({
    dataIndex: 'operation',
    width: 60,
    render: (_, record: { uid: number }) =>
      (dataSource.length >= 1 ? (
        <div onClick={() => handleDelete(record.uid)}>
          <DeleteOutlined />
        </div>
      ) : null),
  });

  const handleAdd = () => {
    const newData: DataType = {
      uid: uniqueId(),
      new: true,
      invalid: true,
      data: array.type === 'object' ? {} : '',
    };
    setDataSource([...dataSource, newData]);
    onChange([...dataSource, newData]);
  };

  const handleDelete = (uid: number) => {
    const newData = dataSource.filter((item) => item.uid !== uid);
    setDataSource(newData);
    onChange(newData);
  };

  const handleSave = (row: DataType, valid: boolean) => {
    const newData = [...dataSource];
    const index = newData.findIndex((item) => row.uid === item.uid);
    const item = newData[index];
    const newDataValue = array.type === 'object' ? row.data : row.data[PRIMITIVE_VALUE_DATA_INDEX];
    newData.splice(index, 1, {
      ...item,
      ...row,
      new: false,
      invalid: !valid,
      data: newDataValue,
    });
    setDataSource(newData);
    onChange(newData);
  };

  const components = {
    body: {
      row: EditableRow,
      cell: EditableCell,
    },
  };

  const columns = defaultColumns.map((col) => {
    if (!col.editable) {
      return col;
    }
    return {
      ...col,
      onCell: (record: DataType) => ({
        record,
        editable: col.editable,
        dataIndex: col.dataIndex,
        title: col.title,
        required: col.required,
        nodeType: col.nodeType,
        handleSave,
      }),
    };
  });

  return (
    <div>
      <Table
        components={components}
        size="small"
        className={styles.factor}
        dataSource={dataSource}
        columns={columns as ColumnTypes}
        pagination={false}
        rowKey={(record) => record.uid}
      />
      <Button onClick={handleAdd} type="link">
        <PlusOutlined />
      </Button>
    </div>
  );
};

export default ArrayForm;
