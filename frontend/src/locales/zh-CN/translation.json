{"navbar": {"officialWebsite": "官网", "docs": "文档", "commercial": "Higress 企业版", "developers": "开发者", "blog": "博客", "community": "社区", "download": "下载"}, "menu": {"dashboard": "监控面板", "serviceSources": "服务来源", "serviceList": "服务列表", "routeConfig": "路由配置", "aiServiceManagement": "AI流量入口管理", "aiRouteManagement": "AI路由管理", "aiDashboard": "AI监控面板", "llmProviderManagement": "AI服务提供者管理", "domainManagement": "域名管理", "certManagement": "证书管理", "consumerManagement": "消费者管理", "pluginManagement": "插件配置"}, "index": {"title": "<PERSON><PERSON><PERSON> Console"}, "init": {"title": "系统初始化", "header": "初始化管理员账号", "usernamePlaceholder": "用户名", "usernameRequired": "请输入用户名", "passwordPlaceholder": "密码", "passwordRequired": "请输入密码", "confirmPasswordPlaceholder": "确认密码", "confirmPasswordRequired": "请确认密码", "confirmPasswordMismatched": "两次输入的密码不一致", "initSuccess": "初始化成功，稍后将跳转至登录页面。", "initFailed": "初始化操作失败。"}, "login": {"title": "登录", "buttonText": "登录", "loginSuccess": "登录成功！", "loginFailed": "登录失败，请重试！", "autoLogin": "自动登录", "forgotPassword": "忘记密码", "usernamePlaceholder": "用户名", "usernameRequired": "请输入用户名！", "passwordPlaceholder": "密码", "passwordRequired": "请输入密码！"}, "aiRoute": {"columns": {"name": "名称", "domains": "域名", "upstreams": "服务", "auth": "请求授权", "pathPredicate": "路径匹配规则", "modelPredicates": "模型匹配规则"}, "routeForm": {"label": {"authConfig": "是否启用请求认证", "authConfigExtra": "启用后，只有包含指定消费者认证信息的请求可以请求本路由。", "authConfigList": "允许请求本路由的消费者名称列表", "domain": "域名", "targetModel": "目标模型", "fallbackConfig": "降级配置", "fallbackConfigExtra": "启用后，若请求目标服务失败，网关会改为请求降级服务。", "fallbackUpstream": "降级服务", "name": "名称", "fallbackResponseCodes": "需要降级的响应码", "serviceName": "服务名称", "serviceWeight": "请求比例", "services": "目标AI服务", "modelPredicates": "模型匹配规则"}, "rule": {"matchTypeRequired": "请选择匹配方式", "matchValueRequired": "请输入匹配规则", "modelNameRequired": "请输入模型名称", "serviceWeightRequired": "请输入请求比例", "fallbackUpstreamRequired": "请选择降级服务", "fallbackResponseCodesRequired": "请选择需要降级的响应码", "nameRequired": "包含小写字母、数字和以及特殊字符(- .)，且不能以特殊字符开头和结尾", "targetServiceRequired": "请选择目标服务", "badWeightSum": "所有服务的权重总和必须为100", "noUpstreams": "至少要配置一个目标AI服务"}, "modelMatchType": "匹配方式", "modelMatchValue": "匹配条件", "byModelName": "按模型名称", "byWeight": "按比例", "addTargetService": "添加目标AI服务", "addModelPredicate": "添加模型匹配规则", "selectModelService": "选择模型服务"}, "create": "创建AI路由", "edit": "编辑AI路由", "deleteConfirmation": "确定删除 <1>{{currentRouteName}}</1> 吗？", "authNotEnabled": "未开启认证", "authEnabledWithoutConsumer": "未授权任何人访问", "usage": "使用方法", "aiRouteUsage": "AI路由使用方法", "aiRouteUsageContent": "可使用以下命令发送请求：", "searchPlaceholder": "根据路由名称、域名、路由条件和目标服务搜索路由"}, "consumer": {"columns": {"name": "消费者名称", "authMethods": "认证方式"}, "create": "创建消费者", "edit": "编辑消费者", "deleteConfirmation": "确定删除 <1>{{currentConsumerName}}</1> 吗？", "consumerForm": {"name": "消费者名称", "nameRequired": "请输入消费者名称", "tokenSourceRequired": "请选择令牌来源", "authTokenRequired": "请输入认证令牌", "headerNameRequired": "请输入Header名称", "paramNameRequired": "请输入参数名称"}, "selectBEARER": "Authorization: Bearer ${value}", "selectHEADER": "自定义 HTTP Header", "selectQUERY": "查询参数", "tokenSource": "令牌来源", "authToken": "认证令牌", "randomGeneration": "随机生成", "headerName": "Header 名称", "paramName": "参数名称", "deleteSuccess": "删除成功"}, "domain": {"columns": {"name": "域名", "protocol": "协议", "certificate": "证书"}, "defaultDomain": "缺省域名", "createDomain": "创建域名", "editDomain": "编辑域名", "deleteConfirmation": "确定删除 <1>{{currentDomainName}}</1> 吗？", "domainForm": {"name": "域名", "nameTooltip": "支持完整域名（例如：hello.com）或模糊域名（例如：*.hello.com），独立管理该域名的协议及证书，且域名能将路由配置相互隔离", "nameRequired": "请输入一个合法的域名", "protocol": "协议", "protocolTooltip": "目前支持HTTP（80端口）和HTTPS（443端口）协议，HTTPS协议必须关联SSL证书", "protocolRequired": "请选择协议", "protocolPlaceholder": "请选择协议", "certificate": "证书", "certificateTooltip": "目前支持阿里云SSL证书服务上的证书", "certificateRequired": "请输入证书", "certificatePlaceholder": "请输入证书", "mustHttps": "是否强制HTTPS", "mustHttpsTooltip": "目前支持阿里云SSL证书服务上的证书", "mustHttpsCheckboxTooltip": "只生效 HTTPS（443端口），HTTP（80端口）访问将被重定向至 HTTPS（443端口）"}}, "dashboard": {"loadFailed": "加载监控面板失败，请稍后刷新页面重试", "uninitialized": "监控面板未自动初始化，点击按钮尝试人工操作", "initDashboard": "初始化监控面板", "noBuiltInDashboard": "未安装Higress Console的内置监控模块。请人工配置监控页面地址。", "configureDashboard": "请配置监控页面URL", "reconfigure": "重新配置", "openInNewPage": "在新窗口打开监控面板", "setForm": {"url": "监控页面URL", "urlRequired": "请输入监控页面URL"}, "configNotes": {"header": "监控面板配置注意事项", "brief": "推荐使用 Prometheus + Grafana 的组合来为 Higress 配置监控面板。", "item1_k8s": "在配置 Prometheus 采集 Higress Gateway Pod 的监控数据时，请增加以下的 relabel_config 以确保可以正常采集到所需的监控数据：", "item1_standalone": "Prometheus 可使用以下 URL 来采集 Higress Gateway 的监控数据时：http://{Gateway服务器IP}:{监控数据端口}/stats/prometheus 其中监控数据端口的默认值为 15020。", "item2": "配置 Grafana 以支持监控面板内嵌显示。", "item2_1": "若使用 grafana.ini 作为配置源，则请在文件中增加以下配置：", "item2_2": "若使用环境变量作为配置源，则请配置以下的环境变量：", "item3": "推荐各位使用 Higress 官方提供的 Grafana 看板配置。您可填写 Grafana 中的 Prometheus 数据源 UID 获取可供导入的 JSON 配置文件。（<1>如何获取数据源 UID？</1>）", "item3_dataSourceId": "Prometheus 数据源 UID", "item3_dataSourceId_required": "请填写 Prometheus 数据源 UID", "item3_templateType": "配置模板", "item3_templateType_MAIN": "通用网关监控模板", "item3_templateType_AI": "AI 网关监控模板", "item3_download": "下载配置文件", "item4": "强烈建议填写 Grafana 的 HTTPS URL 以确保监控面板可以在本页面内正常显示。"}}, "llmProvider": {"columns": {"type": "类型", "name": "名称", "tokens": "凭证"}, "providerTypes": {"openai": "OpenAI", "qwen": "通义千问", "moonshot": "月之暗面", "ai360": "360智脑", "azure": "Azure OpenAI", "baichuan": "百川智能", "baidu": "文心一言", "claude": "Anthropic <PERSON>", "cloudflare": "Cloudflare Workers AI", "cohere": "Cohere", "coze": "扣子", "deepl": "DeepL", "deepseek": "DeepSeek", "doubao": "豆包", "gemini": "Google Gemini", "github": "GitHub模型", "groq": "Groq", "hunyuan": "混元", "minimax": "MiniMax", "mistral": "<PERSON><PERSON><PERSON>", "ollama": "Ollama", "together-ai": "Together AI", "stepfun": "阶跃星辰", "spark": "星火", "yi": "零一万物", "zhipuai": "智谱AI"}, "providerForm": {"label": {"type": "大模型供应商", "failoverEnabled": "令牌降级", "failoverEnabledExtra": "启用后，若某一认证令牌返回异常响应的数量超出网值，Higress 将暂停使用该令牌发起请求，直至后续健康检测请求连续收到一定数量的正常响应。", "failureThreshold": "令牌不可用时需满足的最小连续请求失败次数", "healthCheckInterval": "健康检测请求发起间隔（毫秒）", "healthCheckModel": "健康检测请求使用的模型名称", "healthCheckTimeout": "健康检测请求超时时间（毫秒）", "protocol": "协议", "serviceName": "服务名称", "successThreshold": "令牌可用时需满足的最小连续健康检测成功次数", "azureServiceUrl": "Azure 服务 URL", "ollamaServerHost": "Ollama 服务主机名", "ollamaServerPort": "Ollama 服务端口", "openaiServerType": "OpenAI 服务类型", "openaiCustomUrl": "自定义 OpenAI 服务 BaseURL"}, "rules": {"tokenRequired": "请输入凭证", "typeRequired": "请选择大模型供应商", "serviceNameRequired": "请输入服务名称", "failureThresholdRequired": "请输入最小连续请求失败次数", "healthCheckTimeoutRequired": "请输入健康检测请求超时时间（毫秒）", "healthCheckIntervalRequired": "请输入健康检测请求发起间隔（毫秒）", "healthCheckModelRequired": "请输入健康检测请求使用的模型名称", "protocol": "请选择请求协议", "successThresholdRequired": "请输入最小连续健康检测成功次数", "azureServiceUrlRequired": "请输入 Azure 服务 URL", "ollamaServerHostRequired": "请输入 Ollama 服务主机名", "ollamaServerPortRequired": "请输入 Ollama 服务端口", "openaiCustomUrlRequired": "请输入合法的自定义 OpenAI 服务 BaseURL"}, "placeholder": {"azureServiceUrlPlaceholder": "需包含“/chat/completions”路径和“api-version”查询参数", "ollamaServerHostPlaceholder": "请填写机器名、域名或 IP 地址", "openaiCustomUrlPlaceholder": "示例：https://api.openai.com/v1"}, "openaiServerType": {"official": "OpenAI 官方服务", "custom": "自定义服务"}}, "create": "创建AI服务提供者", "edit": "编辑AI服务提供者", "deleteConfirmation": "确定删除 <1>{{currentLlmProviderName}}</1> 吗？"}, "plugins": {"title": "策略配置", "description": "策略描述", "subTitle": {"domain": "域名名称：", "route": "路由名称：", "aiRoute": "AI路由名称："}, "categories": {"route": "路由", "ai": "AI", "auth": "认证", "security": "安全", "traffic": "流量", "transform": "转换", "o11y": "可观测性", "custom": "自定义"}, "enabled": "已启用", "emptyPlugins": "未启用策略", "addPlugin": "添加插件", "editPlugin": "编辑插件", "addSuccess": "添加成功", "updateSuccess": "修改成功", "deleteConfirmation": "是否确认删除？", "deleteSuccess": "删除成功", "saveSuccess": "保存成功", "configForm": {"targetDomain": "作用域名：", "targetRoute": "作用路由：", "enableStatus": "开启状态", "globalConfigWarning": "注意：以上配置将会在所有域名和路由上生效。请谨慎配置。"}, "builtIns": {"rewrite": {"missingParamError": "重写路径和重写主机域必须要至少填一个", "path": "路径（Path）", "originalPath": "原路径（Path）", "rewritePath": "重写路径（Path）", "rewritePathPlaceholder": "例如：/a", "rewriteType": {"PRE": "前缀重写", "EQUAL": "精确重写"}, "host": "主机域（Host）", "originalHost": "原主机域（Host）", "rewriteHost": "重写主机域（Host）", "rewriteHostPlaceholder": "例如：example.com"}, "headerControl": {"headerType": "Header类型", "request": "请求", "response": "响应", "actionType": "操作类型", "add": "新增", "set": "更新", "remove": "删除", "action": "操作", "addNewRule": "添加新规则", "headerTypeRequired": "请选择Header类型", "actionTypeRequired": "请选择操作类型", "keyRequired": "请输入Header Key", "valueRequired": "请输入Header Value"}, "cors": {"allowOrigins": "允许的访问来源", "allowOriginsRequired": "请输入允许的访问来源", "allowMethods": "允许的方法", "allowMethodsRequired": "请选择允许的方法", "allowHeaders": "允许的请求头部", "allowHeadersRequired": "请输入允许的请求头部", "exposeHeaders": "允许的响应头部", "exposeHeadersRequired": "请输入允许的响应头部", "allowCredentials": "允许携带凭证", "allow": "允许", "disallow": "不允许", "maxAge": "预检的过期时间", "maxAgeRequired": "请输入预检的过期时间"}, "retries": {"attempts": "重试次数", "attemptsRequired": "请输入重试次数", "conditions": "重试条件", "conditionsRequired": "请选择重试条件", "condition": {"error": "error：建立连接失败", "timeout": "timeout：建立连接超时", "non_idempotent": "non_idempotent：对于非幂等请求出错时进行重试"}, "timeout": "超时", "timeoutRequired": "请输入超时时间"}}, "custom": {"title": {"add": "添加插件", "edit": "编辑插件"}, "name": "插件名称", "namePattern": "包含大小写字母，数字以及特殊字符（- .），且不能以特殊字符开头和结尾", "namePlaceholder": "包含大小写字母，数字以及特殊字符（- .），且不能以特殊字符开头和结尾", "description": "插件描述", "descriptionPlaceholder": "请输入插件描述", "imageUrl": "镜像地址", "imageUrlTooltip": "请输入镜像地址，例如：higress-registry.cn-hangzhou.cr.aliyuncs.com/plugins/request-block:1.0.0", "imageUrlPlaceholder": "请输入镜像地址，例如：higress-registry.cn-hangzhou.cr.aliyuncs.com/plugins/request-block:1.0.0", "phase": "插件执行阶段", "phasePlaceholder": "执行阶段先后顺序：认证>鉴权>统计>默认", "priority": "插件执行优先级", "priorityPlaceholder": "范围1～1000，值越大越优先", "imagePullPolicy": "插件拉取策略", "imagePullSecret": "插件拉取密钥", "imagePullSecretPlaceholder": ""}, "phases": {"unspecified": "默认阶段", "authn": "认证阶段", "authz": "鉴权阶段", "stats": "统计阶段"}, "imagePullPolicy": {"unspecified": "默认策略", "ifNotPresent": "仅本地不存在时拉取", "always": "总是拉取"}}, "service": {"columns": {"name": "服务名称（FQDN）", "port": "服务端口", "namespace": "命名空间", "endpoints": "服务地址"}, "name": "服务名称", "namePlaceholder": "请输入服务名称", "namespacePlaceholder": "请输入命名空间"}, "serviceSource": {"types": {"static": {"name": "固定地址"}, "dns": {"name": "DNS域名"}}, "protocols": {"unspecified": "未指定"}, "columns": {"type": "类型", "name": "名称", "domain": "来源地址", "port": "来源端口", "protocol": "服务协议", "action": "操作"}, "createServiceSource": "创建服务来源", "editServiceSource": "编辑服务来源", "deleteConfirmation": "确定删除 <1>{{currentServiceSourceName}}</1> 吗？", "serviceSourceForm": {"type": "类型", "typeTooltip": "目前支持Nacos 1.x、Nacos 2.x、Zookeeper、固定地址和DNS域名等类型", "typePlaceholder": "请选择类型", "name": "名称", "nameRequired": "支持大小写字母、数字和短划线（-），并以字母或数字开头，且不以短划线（-）结尾。长度不超过63个字符。", "namePlaceholder": "支持大小写字母、数字和短划线，并以字母或数字开头，且不以短划线结尾。长度不超过63个字符。", "domain": "注册中心地址", "domainTooltip": "注册中心地址，可以是IP或域名", "domainRequired": "请选择域名", "domainPlaceholder": "支持大小写字母、数字、短划线（-）和星号（*），不超过256个字符。", "port": "注册中心访问端口", "portRequired": "请输入访问端口", "portPlaceholder": "注册中心访问端口", "protocol": "服务协议", "sni": "SNI", "sniPlaceholderForDns": "若服务来源只关联了一个域名且使用与该域名相同的SNI，此处可留空。", "zkServicesPath": "服务注册根路径", "zkServicesPathTooltip": "Zookeeper类型的服务来源需要填写服务注册的根路径。默认监听/dubbo和/services。前者为dubbo服务默认根路径，后者为Spring Cloud服务默认根路径。", "zkServicesPathPlaceholder": "默认监听/dubbo和/services。前者为dubbo服务默认根路径，后者为Spring Cloud服务默认根路径。", "nacosNamespaceId": "Nacos命名空间ID", "nacosNamespaceIdPlaceholder": "留空表示仅监听public命名空间", "nacosNamespaceIdRequired": "请输入Nacos命名空间ID", "nacosGroups": "Nacos服务分组列表", "nacosGroupsRequired": "请输入Nacos服务分组列表", "nacosGroupsPlaceholder": "Nacos服务分组列表", "naco2PortNote": "“以上端口+1000”所得到的端口应同时保持畅通，否则本服务来源将无法正常工作。", "serviceStaticAddresses": "服务地址", "serviceStaticAddressesRequired": "请输入服务地址", "serviceStaticAddressesPlaceholder": "IP:Port（多个地址请以换行分隔）", "servicePort": "服务端口", "servicePortRequired": "请输入服务端口", "servicePortPlaceholder": "支持1-65535的任意端口", "serviceDomains": "域名列表", "serviceDomainsRequired": "请填写域名列表", "serviceDomainsPlaceholder": "服务域名（多个域名请以换行分隔）", "authEnabled": "是否开启了认证", "leaveAuthUnchanged": "如果无需修改认证信息，请留空", "nacosUsername": "Nacos用户名", "nacosUsernameRequired": "请输入Nacos用户名", "nacosPassword": "Nacos密码", "nacosPasswordRequired": "请输入Nacos密码", "consulDatacenter": "Consul数据中心", "consulDatacenterRequired": "请输入Consul数据中心", "consulServiceTag": "Consul服务标签", "consulServiceTagTooltip": "Consul类型的服务来源支持通过标签来过滤服务列表", "consulServiceTagPlaceholder": "示例：foo=bar", "consulToken": "Consul <PERSON><PERSON>", "consulTokenRequired": "请输入Consul Token"}}, "route": {"columns": {"name": "路由名称", "domains": "域名", "routePredicates": "路由条件", "services": "目标服务", "action": "操作"}, "matchTypes": {"PRE": "前缀匹配", "EQUAL": "精确匹配", "REGULAR": "正则匹配"}, "unsupported": "注意：路由配置功能暂不支持版本低于1.19.0的Kubenetes。敬请谅解。", "createRoute": "创建路由", "editRoute": "编辑路由", "deleteConfirmation": "确定删除 <1>{{currentRouteName}}</1> 吗？", "noCustomIngresses": "注：列表中仅包含通过控制台页面创建的路由配置。", "routeSearchPlaceholder": "根据路由名称、域名、路由条件和目标服务搜索路由", "factorGroup": {"columns": {"key": "Key", "matchType": "条件", "matchValue": "值", "operation": "操作"}, "required": {"key": "请输入Key", "matchType": "请选择条件", "matchValue": "请输入值"}, "parameter": "参数"}, "keyValueGroup": {"columns": {"key": "Key", "value": "值", "operation": "操作"}, "required": {"key": "请输入Key", "value": "请输入值"}, "config": "注解"}, "routeForm": {"routeName": "路由名称", "routeNameTip": "推荐结合业务场景命名，例如user-default、user-gray等", "routeNameRequired": "包含小写字母、数字和以及特殊字符(- .)，且不能以特殊字符开头和结尾", "routeNamePlaceholder": "包含小写字母、数字和以及特殊字符(- .)，且不能以特殊字符开头和结尾", "domain": "域名", "domainSearchPlaceholder": "根据域名名称搜索域名。若留空，则表示路由可匹配任意域名", "matchType": "匹配规则", "matchTypeTooltip": "规则之间是“与”关系，即填写的规则越多，匹配的范围越小", "path": "路径（Path）", "pathPredicateRequired": "请选择路径匹配规则", "pathMatcherRequired": "请输入路径匹配值", "pathMatcherPlacedholder": "路径匹配值，如：/user", "caseInsensitive": "忽略大小写", "method": "方法（Method）", "methodMatcherPlaceholder": "方法匹配值，可多选，不填则匹配所有的HTTP方法", "header": "请求头（Header）", "headerTooltip": "多个参数之间是“与”关系", "query": "请求参数（Query）", "queryTooltip": "多个参数之间是“与”关系", "customConfigs": "附加注解（Annotation）", "customConfigsTip": "点击问号查看注解配置说明", "targetService": "目标服务", "targetServiceRequired": "请选择目标服务", "targetServiceNamedPlaceholder": "搜索服务名称选择服务，可多选"}}, "tlsCertificate": {"columns": {"name": "证书名称", "domains": "关联域名列表", "validityStart": "有效期开始时间", "validityEnd": "有效期截止时间", "action": "操作"}, "createTlsCertificate": "创建证书", "editTlsCertificate": "编辑证书", "deleteConfirmation": "确定删除 <1>{{currentTlsCertificateName}}</1> 吗？", "tlsCertificateForm": {"name": "证书名称", "nameTooltip": "全局唯一", "nameRequired": "支持大小写字母、数字、下划线（_）、短划线（-）和星号（*），不超过256个字符。", "namePlaceholder": "支持大小写字母、数字、下划线（_）、短划线（-）和星号（*），不超过256个字符。", "cert": "证书数据", "certRequired": "请输入证书数据", "certPlaceholder": "请输入证书数据", "key": "私钥数据", "keyRequired": "请输入私钥数据", "keyPlaceholder": "请输入私钥数据"}}, "exception": {"info": "错误详情（错误码：<1>{{code}}</1>）"}, "error": {"404": {"title": "404", "subTitle": "你访问的页面不存在"}, "nestedFrame": {"title": "加载异常", "subTitle": "请刷新整个页面"}}, "request": {"error": {"400": "提交内容不合法", "404": "未找到指定资源", "409_post": "指定资源已存在", "409_put": "指定资源已被他人更新。请刷新页面再试。", "500": "服务器内部错误"}}, "user": {"changePassword": {"title": "修改密码", "username": "用户名", "oldPassword": "当前密码", "oldPasswordRequired": "请输入当前密码", "newPassword": "新密码", "newPasswordRequired": "请输入新密码", "confirmPassword": "确认新密码", "confirmPasswordRequired": "请确认新密码", "confirmPasswordMismatched": "两次输入的新密码不一致", "reloginPrompt": "登录密码已更改，请重新登录。"}}, "misc": {"logout": "退出登录", "confirm": "确定", "submit": "提交", "cancel": "取消", "edit": "编辑", "delete": "删除", "search": "查询", "reset": "重置", "return": "返回", "error": "错误", "close": "关闭", "save": "保存", "strategy": "策略", "strategyList": "策略列表", "description": "描述", "configure": "配置", "information": "信息", "action": "操作", "actions": "操作", "seconds": "秒", "tbd": "功能开发中，即将推出...", "yes": "是", "no": "否", "switchToYAML": "YAML视图", "switchToForm": "表单视图", "isRequired": "是必填的", "invalidSchema": "由于 schema 信息无法正常解析，本插件只支持 YAML 编辑方式。"}}