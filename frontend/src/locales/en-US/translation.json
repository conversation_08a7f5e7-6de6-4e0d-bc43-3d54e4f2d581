{"navbar": {"officialWebsite": "OFFICIAL WEBSITE", "docs": "DOCS", "commercial": "HIGRESS IN CLOUD", "developers": "DEVELOPERS", "blog": "BLOG", "community": "COMMUNITY", "download": "DOWNLOAD"}, "menu": {"dashboard": "Dashboard", "serviceSources": "Service Sources", "serviceList": "Service List", "routeConfig": "Route Config", "aiServiceManagement": "AI Service Config", "aiRouteManagement": "AI Route Config", "aiDashboard": "AI Dashboard", "llmProviderManagement": "LLM Provider Management", "domainManagement": "Domain Management", "certManagement": "Certificate Management", "consumerManagement": "Consumer Management", "pluginManagement": "Plugin Management"}, "index": {"title": "<PERSON><PERSON><PERSON> Console"}, "init": {"title": "System Setup", "header": "Setup Admin Account", "usernamePlaceholder": "Username", "usernameRequired": "Please input username", "passwordPlaceholder": "Password", "passwordRequired": "Please input password", "confirmPasswordPlaceholder": "Re-type Password", "confirmPasswordRequired": "Please re-type the password here", "confirmPasswordMismatched": "Password does not match. Enter the password again here.", "initSuccess": "Setup completed. Redirecting to the login page.", "initFailed": "Setup operation failed."}, "login": {"title": "<PERSON><PERSON>", "buttonText": "<PERSON><PERSON>", "loginSuccess": "Login successful!", "loginFailed": "<PERSON><PERSON> failed. Please try again.", "autoLogin": "Auto login", "forgotPassword": "Forgot password", "usernamePlaceholder": "Username", "usernameRequired": "Please input username", "passwordPlaceholder": "Password", "passwordRequired": "Please input password"}, "aiRoute": {"columns": {"name": "Name", "domains": "Domains", "upstreams": "AI Services", "auth": "Request Auth", "pathPredicate": "Path Match Rule", "modelPredicates": "Model Match Rule"}, "routeForm": {"label": {"authConfig": "Request Authentication", "authConfigExtra": "If enabled, only requests containing the auth info of a selected consumer are allowed to use this route.", "authConfigList": "Allowed Consumers", "domain": "Domain", "targetModel": "Target Model", "fallbackConfig": "Token Fallback", "fallbackConfigExtra": "If enabled, gateway will forward the request to the fallback service if error occurs when requesting the target service.", "fallbackUpstream": "Fallback Service", "name": "Name", "fallbackResponseCodes": "Response codes that need fallback", "serviceName": "Service Name", "serviceWeight": "Weight", "services": "Target Services", "modelPredicates": "Model Match Rule"}, "rule": {"matchTypeRequired": "Please select a match type", "matchValueRequired": "Please input the match value", "modelNameRequired": "Please input the model name", "serviceWeightRequired": "Please input the weight value", "fallbackUpstreamRequired": "Please select the fallback service", "fallbackResponseCodesRequired": "Please select the response codes that need fallback", "nameRequired": "Shall only contain upper-and lower-case letters, digits, dashes (-) and dots (.). And cannot end with a dash (-) or dot (.).", "targetServiceRequired": "Please select a target service", "badWeightSum": "The sum of all service weights must be 100.", "noUpstreams": "At least one target service is required."}, "modelMatchType": "Model Match Type", "modelMatchValue": "Match Value", "byModelName": "By model name", "byWeight": "By weight", "addTargetService": "Add target AI service", "addModelPredicate": "Add model match rule", "selectModelService": "Select an AI service"}, "create": "Create AI Route", "edit": "Edit AI Route", "deleteConfirmation": "Are you sure you want to delete <1>{{currentRouteName}}</1>?", "authNotEnabled": "Not enabled", "authEnabledWithoutConsumer": "Nobody authorized", "usage": "Usage", "aiRouteUsage": "AI Route Usage", "aiRouteUsageContent": "You can send a test request using the command below:", "searchPlaceholder": "Search routes by route name, domain, routing conditions and target providers."}, "consumer": {"columns": {"name": "Name", "authMethods": "Auth Methods"}, "create": "Create Consumer", "edit": "Edit Consumer", "deleteConfirmation": "Are you sure you want to delete <1>{{currentConsumerName}}</1>?", "consumerForm": {"name": "Name", "nameRequired": "Please input consumer name", "tokenSourceRequired": "Please choose a token source", "authTokenRequired": "Please input auth token", "headerNameRequired": "Please input header name", "paramNameRequired": "Please input parameter key"}, "selectBEARER": "Authorization: Bearer ${value}", "selectHEADER": "Custom HTTP Header", "selectQUERY": "Query Parameter", "tokenSource": "Token Source", "authToken": "<PERSON><PERSON>", "randomGeneration": "Generate", "headerName": "Header Name", "paramName": "Param Name", "deleteSuccess": "Deleted successfully."}, "domain": {"columns": {"name": "Domain", "protocol": "Protocol", "certificate": "Certificate"}, "defaultDomain": "Default Domain", "createDomain": "Create Domain", "editDomain": "Edit Domain", "deleteConfirmation": "Are you sure you want to delete <1>{{currentDomainName}}</1>?", "domainForm": {"name": "Domain", "nameTooltip": "Both full domain (e.g.: hello.com) and wildcard domain (e.g.: *.hello.com) are supported. Manage the protocol and certificates for a given domain independently here. And route configurations can isolate with each other by different domains.", "nameRequired": "Please input a valid domain.", "protocol": "Protocol", "protocolTooltip": "Following protocols are supported: HTTP (port 80) and HTTPS (port 443) protocols. An SSL certificate must be specified when using HTTPS protocol.", "protocolRequired": "Please choose a protocol", "protocolPlaceholder": "Please choose a protocol", "certificate": "Certificate", "certificateTooltip": "Only certificates provided by SSL Certificates Service of Alibaba Clound are supported now.", "certificateRequired": "Please input the certificate identifier.", "certificatePlaceholder": "Please input the certificate identifier.", "mustHttps": "Force HTTPS", "mustHttpsTooltip": "Only certificates provided by SSL Certificates Service of Alibaba Clound are supported now.", "mustHttpsCheckboxTooltip": "Only HTTPS (port 443) service is available. Requests sent to HTTP (port 80) service will be redirected to the corresponding HTTPS (port 443) service"}}, "dashboard": {"loadFailed": "Error occurs when loading dashboard data. Please refresh the page later.", "uninitialized": "Built-in dashboard is not intialized automatically.Click the button below to initialize it manually.", "initDashboard": "Initialize Dashboard", "noBuiltInDashboard": "Higress Console built-in dashboard is not installed. Please configure one manually.", "configureDashboard": "Please configure the dashboard URL.", "reconfigure": "Reconfigure", "openInNewPage": "Open dashboard in new page", "setForm": {"url": "Dashboard URL", "urlRequired": "Please input the dashboard URL."}, "configNotes": {"header": "Notes on External Dashboard Configuraiton", "brief": "It is recommended to configure Higress dashboard with Prometheus + Grafana.", "item1_k8s": "When adding Prometheus collection configurations to get metrics of Higress Gateway Pods, please add following relabel_config items:", "item1_standalone": "You can use the following URL in Prometheus to get metrics of Higress Gateway: http://{GatewayIP}:{MetricsPort}/stats/prometheus The default value of MetricsPort is 15020.", "item2": "Configure Grafana so dashboard can be displayed within this page.", "item2_1": "If configured with grafana.ini, please add following items into the file:", "item2_2": "If configured with environment variables, please configure following variables:", "item3": "We recommend you using the built-in dashboard configuration of Higress. You can input the UID of the Prometheus data source in Grafana and download the JSON configuration file for import. (<1>How to obtain the UID of a data source?</1>)", "item3_dataSourceId": "Prometheus Data Source UID", "item3_dataSourceId_required": "Please input Prometheus Data Source UID.", "item3_templateType": "Template", "item3_templateType_MAIN": "Generic Gateway Dashboard Template", "item3_templateType_AI": "AI Gateway Dashboard Template", "item3_download": "Download Config File", "item4": "Input the HTTPS URL of Grafana is highly recommended to ensure the dashboard can be shown with in this page correctly."}}, "llmProvider": {"columns": {"type": "Type", "name": "Name", "tokens": "Tokens"}, "providerTypes": {"openai": "OpenAI", "qwen": "<PERSON><PERSON>", "moonshot": "Moonshot", "ai360": "360 Zhinao|", "azure": "Azure OpenAI", "baichuan": "Baichuan AI", "baidu": "ERNIE <PERSON>", "claude": "Anthropic <PERSON>", "cloudflare": "Cloudflare Workers AI", "cohere": "Cohere", "coze": "<PERSON><PERSON>", "deepl": "DeepL", "deepseek": "DeepSeek", "doubao": "Do<PERSON><PERSON>", "gemini": "Google Gemini", "github": "GitHub Models", "groq": "Groq", "hunyuan": "<PERSON>cent <PERSON>", "minimax": "MiniMax", "mistral": "<PERSON><PERSON><PERSON>", "ollama": "Ollama", "together-ai": "Together AI", "stepfun": "Stepfun", "spark": "iFlyTek Spark", "yi": "01.AI", "zhipuai": "Zhipu AI"}, "providerForm": {"label": {"type": "LLM Provider", "failoverEnabled": "<PERSON><PERSON>over", "failoverEnabledExtra": "If enabled, when the count of failed requests with a certain auth token exceeds the threshold, <PERSON><PERSON><PERSON> will no long send requests with it, until following health check requests get a certain number of successes.", "failureThreshold": "Min Consecuitive Failures to <PERSON> a Token", "healthCheckInterval": "Health Check Request Interval (ms)", "healthCheckModel": "Health Check Request LLM Model", "healthCheckTimeout": "Health Check Request Timeout (ms)", "protocol": "Request Procotol", "serviceName": "Service Name", "successThreshold": "Min Consecuitive Sucesses to <PERSON> a Token", "azureServiceUrl": "Azure Service URL", "ollamaServerHost": "Ollama Service Host", "ollamaServerPort": "Ollama Service Port", "openaiServerType": "OpenAI Service Type", "openaiCustomUrl": "Custom OpenAI Service Base URL"}, "rules": {"tokenRequired": "Please input auth token", "typeRequired": "Please select a LLM provider", "serviceNameRequired": "Please input service name", "failureThresholdRequired": "Please input min consecuitive failures to mark down a token", "healthCheckTimeoutRequired": "Please input health check request timeout (ms)", "healthCheckIntervalRequired": "Please input health check request interval (ms)", "healthCheckModelRequired": "Please input health check request LLM model", "protocol": "Please select a request protocol", "successThresholdRequired": "Please input min consecuitive sucesses to mark up a token", "azureServiceUrlRequired": "Please input Azure service URL", "ollamaServerHostRequired": "Please input Ollama service host", "ollamaServerPortRequired": "Please input Ollama service port", "openaiCustomUrlRequired": "Please input a valid custom OpenAI service base URL"}, "placeholder": {"azureServiceUrlPlaceholder": "It shall contain \"/chat/completions\" in the path and \"api-version\" in the query string", "ollamaServerHostPlaceholder": "Please input a hostname, domain name or IP address", "openaiCustomUrlPlaceholder": "Sample: https://api.openai.com/v1"}, "openaiServerType": {"official": "OpenAI Official Service", "custom": "Custom Service"}}, "create": "Create AI Service Provider", "edit": "Edit AI Service Provider", "deleteConfirmation": "Are you sure you want to delete <1>{{currentLlmProviderName}}</1>?"}, "plugins": {"title": "Strategy Configuration", "description": "Strategy Description", "subTitle": {"domain": "Domain Name: ", "route": "Route Name: ", "aiRoute": "AI Route Name: "}, "categories": {"route": "Route", "ai": "AI", "auth": "Authentication", "security": "Security", "traffic": "Traffic", "transform": "Transformation", "o11y": "Observability", "custom": "Custom"}, "enabled": "Enabled", "emptyPlugins": "No plugins configured.", "addPlugin": "<PERSON><PERSON> P<PERSON>in", "editPlugin": "<PERSON> Plugin", "addSuccess": "Added successfully.", "updateSuccess": "Updated successfully.", "deleteConfirmation": "Confirm delete operation?", "deleteSuccess": "Deleted successfully.", "saveSuccess": "Saved successfully.", "configForm": {"targetDomain": "Target Domain: ", "targetRoute": "Target Route: ", "enableStatus": "Enabled", "globalConfigWarning": "Note: Configurations above will be applied to all domains the routes. Please edit with caution."}, "builtIns": {"rewrite": {"missingParamError": "At least one of rewritten path and host shall be provided.", "path": "Path", "originalPath": "Original Path", "rewritePath": "Rewritten Path", "rewritePathPlaceholder": "e.g.: /a", "rewriteType": {"PRE": "Prefix Rewrite", "EQUAL": "Exact Rewrite"}, "host": "Host", "originalHost": "Original Host", "rewriteHost": "Rewritten Host", "rewriteHostPlaceholder": "e.g.: example.com"}, "headerControl": {"headerType": "Header Type", "request": "Request", "response": "Response", "actionType": "Operation Type", "add": "Add", "set": "Set", "remove": "Remove", "action": "Action", "addNewRule": "Add New Rule", "headerTypeRequired": "Please select header type", "actionTypeRequired": "Please select action type", "keyRequired": "Please input header key", "valueRequired": "Please input header value"}, "cors": {"allowOrigins": "Allow Origins", "allowOriginsRequired": "Please input allow origins", "allowMethods": "Allow Methods", "allowMethodsRequired": "Please select allow methods", "allowHeaders": "Allow Headers", "allowHeadersRequired": "Please input allow headers", "exposeHeaders": "Expose Headers", "exposeHeadersRequired": "Please input expose headers", "allowCredentials": "Allow Credentials", "allow": "Allow", "disallow": "Disallow", "maxAge": "Max Age of Pre-flight Requests", "maxAgeRequired": "Please input the max cache age of pre-flight requests"}, "retries": {"attempts": "Retry Attempts", "attemptsRequired": "Please input retry attempts", "conditions": "Retry Conditions", "conditionsRequired": "Please select retry conditions", "condition": {"error": "error: Unable to establish connections", "timeout": "timeout: Timeout when trying to establish connections", "non_idempotent": "non_idempotent: Retry when a non-idempotent request fails"}, "timeout": "Timeout", "timeoutRequired": "Please input timeout value"}}, "custom": {"title": {"add": "<PERSON><PERSON> P<PERSON>in", "edit": "<PERSON> Plugin"}, "name": "Plugin Name", "namePattern": "Shall only contain upper-and lower-case letters, digits, dashes (-) and dots (.). And cannot end with a dash (-) or dot (.).", "namePlaceholder": "Shall only contain upper-and lower-case letters, digits, dashes (-) and dots (.). And cannot end with a dash (-) or dot (.).", "description": "Plugin Description", "descriptionPlaceholder": "Please input plugin description", "imageUrl": "Image URL", "imageUrlTooltip": "Please input image URL. e.g.: higress-registry.cn-hangzhou.cr.aliyuncs.com/plugins/request-block:1.0.0", "imageUrlPlaceholder": "Please input image URL. e.g.: higress-registry.cn-hangzhou.cr.aliyuncs.com/plugins/request-block:1.0.0", "phase": "Plugin Execution Phase", "phasePlaceholder": "The sequence of phases: AuthN > AuthZ > Stats > Default", "priority": "Plugin Execution Priority", "priorityPlaceholder": "Range from 1 to 1000. Plugins with a larger priority will execute first.", "imagePullPolicy": "<PERSON><PERSON><PERSON>", "imagePullSecret": "<PERSON>lug<PERSON>", "imagePullSecretPlaceholder": ""}, "phases": {"unspecified": "<PERSON><PERSON><PERSON>", "authn": "AuthN", "authz": "AuthZ", "stats": "Stats"}, "imagePullPolicy": {"unspecified": "<PERSON><PERSON><PERSON>", "ifNotPresent": "IfNotPresent", "always": "Always"}}, "service": {"columns": {"name": "Service Name (FQDN)", "port": "Port", "namespace": "Namespace", "endpoints": "Endpoints"}, "name": "Service Name", "namePlaceholder": "Please input the service name.", "namespacePlaceholder": "Please input the namespace."}, "serviceSource": {"types": {"static": {"name": "Static Addresses"}, "dns": {"name": "Domains"}}, "protocols": {"unspecified": "Unspecified"}, "columns": {"type": "Type", "name": "Name", "domain": "Source Domain", "port": "Source Port", "protocol": "Service Protocol", "action": "Action"}, "createServiceSource": "Create Service Source", "editServiceSource": "Edit Service Source", "deleteConfirmation": "Are you sure you want to delete <1>{{currentServiceSourceName}}</1>?", "serviceSourceForm": {"type": "Type", "typeTooltip": "Following service source types are not supported: Nacos 1.x, Nacos2.x, Zookeeper, static addresses and domains.", "typePlaceholder": "Please select the type.", "name": "Name", "nameRequired": "Shall only contain upper-and lower-case letters, digits and dashes (-). Must begin with a letter or digit. Cannot end with a dash (-). No longer than 63 characters.", "namePlaceholder": "Please input a valid name.", "domain": "Registry Domain", "domainTooltip": "It can be either an IP address or a domain name.", "domainRequired": "Please select a domain name", "domainPlaceholder": "Only following inputs are allowed: upper-and lower-case letters, numbers, dash (-) and asterisk (*). No longer than 256 characters.", "port": "Registry Port", "portRequired": "Please input the port.", "portPlaceholder": "Registry Port", "protocol": "Service Protocol", "sni": "SNI", "sniPlaceholderForDns": "Leave it empty if only one domain is set and to be used as SNI.", "zkServicesPath": "Service Registration Root Path", "zkServicesPathTooltip": "The root path of service registration are required for Zookeeper service sources. /dubbo and /services are listened by default. The former is the default root path of dubbo services, and the latter is the default root path of Sprin gCloud services", "zkServicesPathPlaceholder": "/dubbo and /services are listened by default. The former is the default root path of dubbo service, and the latter is the default root path of Spring Cloud services.", "nacosNamespaceId": "ID of the Nacos Namespace", "nacosNamespaceIdPlaceholder": "Leave it empty to watch the public namespace only.", "nacosNamespaceIdRequired": "Please input the ID of the Nacos namespace.", "nacosGroups": "Nacos Service Groups", "nacosGroupsRequired": "Please input Nacos service groups.", "nacosGroupsPlaceholder": "Nacos Service Groups", "naco2PortNote": "The port calculated by \"PortAbove+1000\" shall be kept accessiable as well. Otherwise, the service source won't function properly.", "serviceStaticAddresses": "Service Addresses", "serviceStaticAddressesRequired": "Please input service addresses.", "serviceStaticAddressesPlaceholder": "IP:Port (One address per line)", "servicePort": "Service Port", "servicePortRequired": "Please input service port.", "servicePortPlaceholder": "Any port within the range of 1-65535 is supported.", "serviceDomains": "Domains", "serviceDomainsRequired": "Please input domains", "serviceDomainsPlaceholder": "Service domain (One domain per line)", "authEnabled": "Authentication Enabled", "leaveAuthUnchanged": "Please leave the fields below empty to keep the saved credential.", "nacosUsername": "Nacos Username", "nacosUsernameRequired": "Please input Nacos username", "nacosPassword": "Nacos Password", "nacosPasswordRequired": "Please input Nacos password", "consulDatacenter": "Consul Data Center", "consulDatacenterRequired": "Please input Consul data center", "consulServiceTag": "Consul Service Tag", "consulServiceTagTooltip": "You can filter services by a tag in Consul service sources.", "consulServiceTagPlaceholder": "e.g.: foo=bar", "consulToken": "Consul <PERSON><PERSON>", "consulTokenRequired": "Please input Consul token"}}, "route": {"columns": {"name": "Route Name", "domains": "Domain", "routePredicates": "Route Predicates", "services": "Service", "action": "Action"}, "matchTypes": {"PRE": "Prefix", "EQUAL": "Exact", "REGULAR": "Regex"}, "unsupported": "Note: Route management feature doesn't support Kubenetes with version < 1.19.0 at the moment.", "createRoute": "Create Route", "editRoute": "Edit Route", "deleteConfirmation": "Are you sure you want to delete <1>{{currentRouteName}}</1>?", "noCustomIngresses": "Note: Only routes created from the console are listed above.", "routeSearchPlaceholder": "Search routes by route name, domain, routing conditions and target service.", "factorGroup": {"columns": {"key": "Key", "matchType": "Condition", "matchValue": "Value", "operation": "Action"}, "required": {"key": "Please input match key.", "matchType": "Please select match type.", "matchValue": "Please input match value."}, "parameter": "Parameter"}, "keyValueGroup": {"columns": {"key": "Key", "value": "ֵValue", "operation": "Action"}, "required": {"key": "Please input annotation key.", "value": "Please input annotation value."}, "config": "Annotation"}, "routeForm": {"routeName": "Route Name", "routeNameTip": "Using a name related to the business scenario is recommended. e.g.: user-default, user-gray, etc.", "routeNameRequired": "Only following characters are allowed: lower-case letters, numbers and special characters (- .). And the name can not begin or end with a special character.", "routeNamePlaceholder": "Only following characters are allowed: lower-case letters, numbers and special characters (- .). And the name can not begin or end with a special character.", "domain": "Domain", "domainSearchPlaceholder": "Search domain by name. If left empty, it will match any domain.", "matchType": "Match Type", "matchTypeTooltip": "The relation among different arguments is \"and\", which means the more rules are configured, the smaller the range to match is", "path": "Path", "pathPredicateRequired": "Please select a path matching predicate.", "pathMatcherRequired": "Please input the path match target.", "pathMatcherPlacedholder": "Path match target. e.g.: /user", "caseInsensitive": "Case Insensitive", "method": "Methods", "methodMatcherPlaceholder": "HTTP methods to match. Multiple selections are allowed. If left blank, it will match all the HTTP methods.", "header": "Request Headers", "headerTooltip": "The relation among different arguments is \"and\".", "query": "Query Parameters", "queryTooltip": "The relation among different arguments is \"and\".", "customConfigs": "Custom Annotations", "customConfigsTip": "Click this \"?\" to view the configuration guide.", "targetService": "Target Service", "targetServiceRequired": "Please choose the target service.", "targetServiceNamedPlaceholder": "Search target service by name. Multiple selections are allowed."}}, "tlsCertificate": {"columns": {"name": "Certificate Name", "domains": "Associated Domains", "validityStart": "<PERSON><PERSON>", "validityEnd": "<PERSON>id <PERSON>", "action": "Action"}, "createTlsCertificate": "Create Certificate", "editTlsCertificate": "Edit Certificate", "deleteConfirmation": "Are you sure you want to delete <1>{{currentTlsCertificateName}}</1>?", "tlsCertificateForm": {"name": "Certificate Name", "nameTooltip": "Globally unique", "nameRequired": "Only following inputs are allowed: upper-and lower-case letters, numbers, underscore (_), dash (-) and asterisk (*). No longer than 256 characters.", "namePlaceholder": "Only following inputs are allowed: upper-and lower-case letters, numbers, underscore (_), dash (-) and asterisk (*). No longer than 256 characters.", "cert": "Certificate Data", "certRequired": "Please enter certificate data", "certPlaceholder": "Please enter certificate data", "key": "Certificate Key", "keyRequired": "Please enter certificate key", "keyPlaceholder": "Please enter certificate key"}}, "exception": {"info": "Error details (Error code: <1>{{code}}</1>)"}, "error": {"404": {"title": "404", "subTitle": "Page Not Found"}, "nestedFrame": {"title": "Error", "subTitle": "Please refresh the entire page."}}, "request": {"error": {"400": "Bad request", "404": "Resource not found", "409_post": "Resource already exists", "409_put": "Resource is already updated by someone else. Please refresh and try again.", "500": "Internal server error"}}, "user": {"changePassword": {"title": "Change Password", "username": "Username", "oldPassword": "Current Password", "oldPasswordRequired": "Please enter the current password", "newPassword": "New Password", "newPasswordRequired": "Please enter the new password", "confirmPassword": "Re-type New Password", "confirmPasswordRequired": "Please re-type the new password here", "confirmPasswordMismatched": "New password does not match. Enter new password again here.", "reloginPrompt": "Password is changed successfully. Please re-login."}}, "misc": {"logout": "Logout", "confirm": "Confirm", "submit": "Submit", "cancel": "Cancel", "edit": "Edit", "delete": "Delete", "search": "Search", "reset": "Reset", "return": "Return", "error": "Error", "close": "Close", "save": "Save", "strategy": "Strategy", "strategyList": "Strategy List", "description": "Description", "configure": "Configure", "information": "Information", "action": "Action", "actions": "Actions", "seconds": "Sec(s)", "tbd": "Still in development. To be released soon...", "yes": "Yes", "no": "No", "switchToYAML": "YAML View", "switchToForm": "Form View", "isRequired": "is required", "invalidSchema": "Since schema information cannot be properly parsed, this plugin only supports YAML editing."}}