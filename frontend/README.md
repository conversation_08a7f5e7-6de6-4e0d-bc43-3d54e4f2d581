# higress console

## Usage

```bash
$ npm install

$ npm start
```

## Directories

```md
.
├── README.md
├── ice.config.mts                  # The project config
├── package.json
├── .browserslistrc                 # Browsers that we support
├── public
│   ├── favicon.ico   
├── src
|  ├── app.ts                       # App entry
|  ├── assets
|  ├── components                   # Common component
|  ├── document.tsx
|  ├── global.css                   # Global style
|  ├── interfaces
|  ├── menuConfig.tsx               # Layout menus
|  ├── models
|  ├── pages                        # Pages directory
|  ├── services
|  ├── store.ts                     # App store
|  └── typings.d.ts
└── tsconfig.json
```

For more details, please visit [docs](https://v3.ice.work/).
