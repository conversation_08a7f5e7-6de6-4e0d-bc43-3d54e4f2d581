{"name": "@ice/antd-pro-scaffold", "version": "0.1.0", "description": "higress console", "dependencies": {"@ant-design/charts": "^1.4.2", "@ant-design/icons": "^4.7.0", "@ant-design/pro-components": "^2.3.51", "@ant-design/pro-form": "^2.2.8", "@ant-design/pro-layout": "^7.1.6", "@ant-design/pro-table": "^3.0.17", "@ice/runtime": "^1.0.0", "@monaco-editor/react": "^4.4.6", "antd": "^4.24.0", "axios": "^1.2.1", "i18next": "^22.4.9", "i18next-browser-languagedetector": "^7.0.1", "i18next-http-backend": "^2.1.1", "js-yaml": "^4.1.0", "monaco-editor": "^0.37.0", "monaco-yaml": "^4.0.4", "react": "^18.2.0", "react-chatgpt-modal": "^0.1.8", "react-dom": "^18.2.0", "react-i18next": "^12.1.4", "react-syntax-highlighter": "^15.6.1"}, "devDependencies": {"@ice/app": "^3.0.0", "@ice/plugin-auth": "^1.0.0", "@ice/plugin-request": "^1.0.0", "@ice/plugin-store": "^1.0.0", "@iceworks/spec": "^1.0.0", "@types/js-yaml": "^4.0.9", "@types/react": "^18.0.0", "@types/react-dom": "^18.0.0", "@typescript-eslint/eslint-plugin": "^5.60.1", "ahooks": "^3.7.2", "cross-env": "^7.0.3", "eslint": "7.30.0", "husky": "^8.0.3", "ice.js": "^2.0.0", "lint-staged": "^13.1.2", "lodash": "^4.17.21", "qs": "^6.11.0", "stylelint": "13.2.1", "typescript": "^4.4.4"}, "scripts": {"prepare": "cd .. && husky install frontend/.husky", "start": "cross-env NODE_ENV=development && ice start", "build": "cross-env NODE_ENV=production && ice build", "build-dev": "cross-env NODE_ENV=development && ice build", "lint": "npm run eslint && npm run stylelint", "lint:lint-staged": "lint-staged", "eslint": "eslint --cache --ext .js,.ts,.jsx,.tsx ./", "eslint:fix": "npm run eslint -- --fix", "stylelint": "stylelint \"**/*.{css,scss,less}\""}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "private": true, "originTemplate": "@ice/antd-pro-scaffold"}