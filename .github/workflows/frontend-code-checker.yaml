name: "Frontend Code Checker"

on:
  push:
    branches: ["main"]
  pull_request:
    branches: ["*"]

jobs:
  build:
    runs-on: ubuntu-latest

    strategy:
      matrix:
        node-version: [16.x]

    steps:
      - uses: actions/checkout@v3

      - name: Use Node.js ${{ matrix.node-version }}
        uses: actions/setup-node@v3
        with:
          node-version: ${{ matrix.node-version }}

      - name: cd frontend && install
        run: |
          cd frontend && npm install
      - name: cd frontend && npm run lint
        run: |
          cd frontend && npm run lint
