name: "Build and Test"

on:
  push:
    branches: [ "main" ]
  pull_request:
    branches: [ "*" ]

jobs:
  build:
    runs-on: ubuntu-latest
    steps:
      - name: "Setup Java"
        uses: actions/setup-java@v3
        with:
          java-version: 21
          distribution: 'temurin'

      - name: "checkout ${{ github.ref }}"
        uses: actions/checkout@v3
        with:
          fetch-depth: 1

      - name: Build Higress Console Package
        run: mvn clean package -f ./backend/pom.xml -Dpmd.language=en

      - name: Upload Higress Console Package
        uses: actions/upload-artifact@v4
        with:
          name: higress-console
          path: backend/target/*.jar
